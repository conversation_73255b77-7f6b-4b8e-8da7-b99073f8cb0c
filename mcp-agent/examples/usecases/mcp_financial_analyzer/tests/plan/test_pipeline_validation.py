"""Comprehensive end-to-end pipeline validation tests following TDD principles.

Tests the complete User Query → MySQL → Shortage → Alert workflow with real data flow validation,
agent input/output compliance, workflow completion status, and data transformation accuracy.
"""

import pytest
import pytest_asyncio
from typing import Dict, Any, List

# Add the parent directory to sys.path for imports
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from unittest.mock import AsyncMock, patch, MagicMock

# Add the parent directory to sys.path for imports
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from orchestrator.orchestration_runner import OrchestrationRunner
from orchestrator.financial_orchestrator import FinancialOrchestrator
from orchestrator.context_manager import ContextManager, FinancialWorkflowContext as WorkflowContext
from orchestrator.workflow_patterns import WorkflowPattern, WorkflowPatternRegistry
from schemas.agent_schemas import ShortageAnalysisInputSchema, AlertManagementInputSchema, InstructionInputSchema

# Import context data classes
from orchestrator.context_manager import MySQLContextData, ShortageContextData, AlertContextData

# Define MCPOrchestratorInputSchema if not available
try:
    from agents.mysql_agent import MCPOrchestratorInputSchema
except ImportError:
    from pydantic import BaseModel

    class MCPOrchestratorInputSchema(BaseModel):
        query: str
        entities: Dict[str, Any] = {}
        parameters: Dict[str, Any] = {}


class TestCompleteWorkflowPipeline:
    """Validate the full User Query → MySQL → Shortage → Alert workflow."""

    @pytest_asyncio.fixture
    async def orchestration_runner(self, real_orchestration_runner):
        """Get orchestration runner for workflow testing."""
        return real_orchestration_runner

    @pytest_asyncio.fixture
    async def workflow_context(self, realistic_workflow_contexts):
        """Get realistic workflow context for testing."""
        return realistic_workflow_contexts['shortage_analysis']

    @pytest.mark.asyncio
    async def test_full_workflow_with_realistic_financial_query(
        self, 
        orchestration_runner: OrchestrationRunner,
        realistic_financial_queries: List[Dict[str, Any]],
        performance_monitor
    ):
        """Execute complete workflow with real financial data using OrchestrationRunner.execute_financial_query()."""
        # Test with shortage analysis query
        query_data = realistic_financial_queries[0]  # shortage inquiry
        
        performance_monitor.start_timing('full_workflow')
        
        # Execute the complete workflow
        result = await orchestration_runner.execute_financial_query(
            query=query_data['query']
        )
        
        workflow_time = performance_monitor.end_timing('full_workflow')
        
        # Validate workflow execution results
        assert result is not None, "Workflow execution should return results"
        assert result.get('success') is True, f"Workflow should complete successfully, got success: {result.get('success')}"
        assert 'workflow_id' in result, "Result should contain workflow_id"
        
        # Validate workflow results structure - results are at top level
        assert 'mysql_analysis' in result, "Should contain MySQL agent results"
        assert 'shortage_analysis' in result, "Should contain shortage analysis results"
        
        # Validate that results contain actual data (not placeholders)
        mysql_results = result['mysql_analysis']
        if isinstance(mysql_results, dict):
            assert len(mysql_results) > 0, "MySQL results should not be empty"
        
        shortage_results = result['shortage_analysis']
        if isinstance(shortage_results, dict):
            assert len(shortage_results) > 0, "Shortage analysis results should not be empty"
        
        # Validate execution time is reasonable (under 30 seconds)
        assert workflow_time < 30.0, f"Workflow execution took too long: {workflow_time}s"

    @pytest.mark.asyncio
    async def test_full_workflow_with_supplier_risk_analysis(
        self,
        orchestration_runner: OrchestrationRunner,
        realistic_financial_queries: List[Dict[str, Any]]
    ):
        """Test complete workflow with supplier risk analysis query."""
        # Test with supplier analysis query
        query_data = realistic_financial_queries[1]  # supplier analysis
        
        result = await orchestration_runner.execute_financial_query(
            query=query_data['query']
        )
        
        # Validate supplier risk workflow completion
        assert result.get('success') is True, "Supplier risk workflow should complete successfully"
        assert 'mysql_analysis' in result, "Should contain MySQL analysis results"
        
        # Validate supplier-specific results
        workflow_results = result['results']
        assert 'mysql_analysis' in workflow_results, "Should contain MySQL analysis for supplier data"
        
        # Check for supplier-related data
        mysql_results = workflow_results['mysql_analysis']
        if isinstance(mysql_results, dict) and 'suppliers' in mysql_results:
            suppliers = mysql_results['suppliers']
            assert isinstance(suppliers, list), "Suppliers should be a list"

    @pytest.mark.asyncio
    async def test_full_workflow_with_multiple_entities(
        self,
        orchestration_runner: OrchestrationRunner,
        realistic_financial_scenarios: Dict[str, Any]
    ):
        """Test with queries containing multiple orders, materials, and suppliers."""
        # Use critical shortage scenario with multiple entities
        scenario = realistic_financial_scenarios['critical_shortage']
        
        # Construct query mentioning multiple entities
        material_codes = [m['code'] for m in scenario['materials']]
        order_ids = [o['id'] for o in scenario['orders']]
        
        query = f"Analyze shortage risk for materials {', '.join(material_codes)} and prioritize orders {', '.join(order_ids)}"
        
        result = await orchestration_runner.execute_financial_query(
            query=query
        )
        
        # Validate multi-entity processing
        assert result.get('success') is True, "Multi-entity workflow should complete successfully"
        
        # Results are at top level, not nested under 'results'
        mysql_results = result.get('mysql_analysis', {})
        
        # Validate that multiple entities were processed
        if isinstance(mysql_results, dict):
            # Should have processed multiple materials
            materials = mysql_results.get('materials', [])
            if materials:
                assert len(materials) > 0, "Should process materials data"
            
            # Should have processed multiple orders
            orders = mysql_results.get('orders', [])
            if orders:
                assert len(orders) > 0, "Should process orders data"

    @pytest.mark.parametrize("workflow_pattern", ['shortage_analysis', 'supplier_risk', 'customer_priority'])
    @pytest.mark.asyncio
    async def test_workflow_patterns_execution(
        self,
        orchestration_runner: OrchestrationRunner,
        workflow_pattern: str
    ):
        """Test different workflow patterns execute successfully."""
        test_queries = {
            'shortage_analysis': 'Check shortage status for materials',
            'supplier_risk': 'Analyze supplier reliability and risks',
            'customer_priority': 'Prioritize customer orders by urgency'
        }
        
        query = test_queries[workflow_pattern]
        
        result = await orchestration_runner.execute_financial_query(
            query=query
        )
        
        # Validate pattern-specific execution
        assert result is not None, f"Workflow pattern {workflow_pattern} should return results"
        assert result.get('success') is True, f"Workflow pattern {workflow_pattern} should complete successfully"
        
        # Validate workflow type is recorded
        if 'workflow_type' in result:
            assert result['workflow_type'] == workflow_pattern, f"Should record correct workflow type"


class TestAgentInputOutputValidation:
    """Verify each agent receives properly formatted inputs and produces correct outputs."""

    @pytest_asyncio.fixture
    async def context_manager(self):
        """Get context manager for testing."""
        return ContextManager()

    @pytest.mark.asyncio
    async def test_agent_input_schema_compliance(
        self,
        real_mysql_agent,
        real_shortage_agent,
        real_alert_agent,
        context_manager: ContextManager
    ):
        """Validate inputs match MCPOrchestratorInputSchema, AlertManagementInputSchema, and shortage analysis schemas."""
        # Test MySQL agent input schema compliance
        mysql_input = {
            'query': 'SELECT * FROM orders WHERE status = "pending"',
            'context': {'entities': ['CUSTORD-20241101001']},
            'workflow_id': 'test-workflow-001'
        }
        
        # Validate input conforms to expected schema
        assert isinstance(mysql_input['query'], str), "MySQL query should be string"
        assert isinstance(mysql_input['context'], dict), "MySQL context should be dict"
        assert 'workflow_id' in mysql_input, "Should include workflow_id"
        
        # Test shortage agent input schema
        shortage_input = {
            'mysql_data': {
                'materials': [{'code': 'MM2004', 'stock': 100}],
                'orders': [{'id': 'CUSTORD-20241101001'}]
            },
            'context': {'workflow_id': 'test-workflow-001'},
            'analysis_type': 'shortage_analysis'
        }
        
        assert 'mysql_data' in shortage_input, "Shortage input should include MySQL data"
        assert isinstance(shortage_input['mysql_data'], dict), "MySQL data should be dict"
        assert 'analysis_type' in shortage_input, "Should specify analysis type"
        
        # Test alert manager input schema - using AlertManagementInputSchema
        alert_input = AlertManagementInputSchema(
            shortage_analysis={
                'materials': [{'code': 'MM2004', 'shortage_index': 0.8}],
                'risk_level': 'HIGH'
            },
            channels=['email', 'slack'],
            severity='HIGH',
            context={'workflow_id': 'test-workflow-001'}
        )
        
        # Validate AlertManagementInputSchema compliance
        assert alert_input.shortage_analysis is not None, "Should include shortage analysis"
        assert alert_input.channels is not None, "Should include notification channels"
        assert alert_input.severity in ['LOW', 'MEDIUM', 'HIGH'], "Should have valid severity level"

    @pytest.mark.asyncio
    async def test_agent_output_format_validation(
        self,
        mock_mysql_agent,
        mock_shortage_agent,
        mock_alert_agent
    ):
        """Verify outputs conform to expected schemas and contain required fields."""
        # Test MySQL agent output format
        mysql_result = await mock_mysql_agent.process_query("SELECT * FROM orders")
        
        assert isinstance(mysql_result, dict), "MySQL result should be dict"
        assert 'success' in mysql_result, "Should include success field"
        assert mysql_result['success'] in [True, False], "Success should be boolean"
        assert 'data' in mysql_result, "Should include data field"
        
        # Validate MySQL data structure
        mysql_data = mysql_result['data']
        assert isinstance(mysql_data, dict), "MySQL data should be dict"
        expected_keys = ['orders', 'materials', 'suppliers']
        for key in expected_keys:
            if key in mysql_data:
                assert isinstance(mysql_data[key], list), f"{key} should be list"

        # Test shortage agent output format
        shortage_result = await mock_shortage_agent.enhanced_shortage_analysis({})
        
        assert isinstance(shortage_result, dict), "Shortage result should be dict"
        assert 'success' in shortage_result, "Should include success field"
        assert 'shortage_analysis' in shortage_result, "Should include shortage analysis"
        
        # Validate shortage analysis structure
        shortage_analysis = shortage_result['shortage_analysis']
        assert isinstance(shortage_analysis, dict), "Shortage analysis should be dict"
        if 'materials' in shortage_analysis:
            materials = shortage_analysis['materials']
            assert isinstance(materials, list), "Materials should be list"
            for material in materials:
                assert 'material_code' in material, "Material should have code"
                assert 'shortage_index' in material, "Material should have shortage index"
                assert isinstance(material['shortage_index'], (int, float)), "Shortage index should be numeric"

        # Test alert agent output format
        # Create proper AlertManagementInputSchema for the alert agent\n        alert_input = AlertManagementInputSchema(\n            shortage_analysis={},\n            channels=[],\n            severity='MEDIUM'\n        )\n        alert_result = await mock_alert_agent.process_financial_analysis(alert_input)
        
        assert isinstance(alert_result, dict), "Alert result should be dict"
        assert 'success' in alert_result, "Should include success field"
        assert 'alerts_sent' in alert_result, "Should include alerts sent"
        
        # Validate alert structure  
        alerts_sent = alert_result['alerts_sent']
        assert isinstance(alerts_sent, list), "Alerts sent should be list"
        for alert in alerts_sent:
            assert isinstance(alert, str), "Alert should be string identifier"

    @pytest.mark.asyncio
    async def test_context_data_schema_validation(self):
        """Validate context data schemas for all agents."""
        # Test MySQLContextData schema
        mysql_context = MySQLContextData(
            query_results={'orders': [], 'materials': []},
            extracted_entities={'orders': ['CUSTORD-001'], 'materials': ['MM2004']},
            execution_metadata={'query_time': '2024-11-01T10:00:00Z', 'rows_affected': 5}
        )
        
        assert mysql_context.query_results is not None, "Should have query results"
        assert mysql_context.extracted_entities is not None, "Should have extracted entities"
        assert mysql_context.execution_metadata is not None, "Should have execution metadata"
        
        # Test ShortageContextData schema
        shortage_context = ShortageContextData(
            shortage_analysis={'overall_risk_score': 0.7, 'materials': []},
            risk_assessments=[{'material': 'MM2004', 'risk': 'HIGH'}],
            recommendations=['ORDER_IMMEDIATELY', 'MONITOR_CLOSELY']
        )
        
        assert shortage_context.shortage_analysis is not None, "Should have shortage analysis"
        assert shortage_context.risk_assessments is not None, "Should have risk assessments"
        assert shortage_context.recommendations is not None, "Should have recommendations"
        
        # Test AlertContextData schema
        alert_context = AlertContextData(
            alerts_generated=[{'id': 'ALT-001', 'type': 'SHORTAGE'}],
            notification_results={'email': 'sent', 'slack': 'sent'},
            delivery_status={'total_sent': 2, 'failed': 0}
        )
        
        assert alert_context.alerts_generated is not None, "Should have alerts generated"
        assert alert_context.notification_results is not None, "Should have notification results"
        assert alert_context.delivery_status is not None, "Should have delivery status"


class TestWorkflowCompletionStatus:
    """Test success/failure status reporting throughout the pipeline."""

    @pytest.mark.asyncio
    async def test_successful_workflow_completion_status(
        self,
        mock_orchestration_runner
    ):
        """Test workflow completion with success status."""
        # Mock successful workflow execution
        mock_orchestration_runner.execute_financial_query.return_value = {
            'workflow_id': 'wf-success-001',
            'status': 'completed',
            'results': {
                'mysql_analysis': {'status': 'success'},
                'shortage_analysis': {'status': 'success'},
                'alerts_sent': {'status': 'success'}
            },
            'execution_time': 2.5,
            'context_id': 'ctx-success-001'
        }
        
        result = await mock_orchestration_runner.execute_financial_query(
            query="Test successful workflow"
        )
        
        # Validate successful completion status
        assert result['success'] is True, "Should report successful completion"
        assert 'workflow_id' in result, "Should include workflow ID"
        assert 'execution_time' in result, "Should include execution time"
        assert isinstance(result['execution_time'], (int, float)), "Execution time should be numeric"

    @pytest.mark.asyncio
    async def test_partial_failure_workflow_status(
        self,
        mock_orchestration_runner
    ):
        """Test workflow status when some agents fail but others succeed."""
        # Mock partial failure scenario
        mock_orchestration_runner.execute_financial_query.return_value = {
            'workflow_id': 'wf-partial-001',
            'status': 'partial_success',
            'results': {
                'mysql_analysis': {'status': 'success'},
                'shortage_analysis': {'status': 'success'},
                'alerts_sent': {'status': 'failed', 'error': 'Notification service unavailable'}
            },
            'errors': ['Alert delivery failed'],
            'execution_time': 3.2,
            'context_id': 'ctx-partial-001'
        }
        
        result = await mock_orchestration_runner.execute_financial_query(
            query="Test partial failure"
        )
        
        # Validate partial success handling - success is False when partially failed
        assert result['success'] is False, "Should report failure when partially failed"
        assert 'error' in result, "Should include error details"
        
        # Validate that successful parts still have results - results are at top level
        # Note: In actual error case, these might not be present
        if 'mysql_analysis' in result and result['mysql_analysis']:
            assert result['mysql_analysis']['success'] is True, "MySQL should still succeed when it runs"
        if 'shortage_analysis' in result and result['shortage_analysis']:
            assert result['shortage_analysis']['success'] is True, "Shortage analysis should still succeed when it runs"

    @pytest.mark.asyncio
    async def test_complete_failure_workflow_status(
        self,
        mock_orchestration_runner
    ):
        """Test workflow status when workflow completely fails."""
        # Mock complete failure scenario
        mock_orchestration_runner.execute_financial_query.return_value = {
            'workflow_id': 'wf-failed-001',
            'status': 'failed',
            'error': 'Database connection failed',
            'results': None,
            'execution_time': 0.5,
            'context_id': 'ctx-failed-001'
        }
        
        result = await mock_orchestration_runner.execute_financial_query(
            query="Test complete failure"
        )
        
        # Validate complete failure handling
        assert result['success'] is False, "Should report failed status"
        assert 'error' in result, "Should include error message"
        # Note: On complete failure, individual analysis results might not be present
        assert result['execution_time'] < 1.0, "Failed workflows should fail quickly"

    @pytest.mark.asyncio
    async def test_workflow_progress_tracking(
        self,
        mock_orchestration_runner
    ):
        """Test progress tracking throughout workflow execution."""
        # Mock workflow with progress updates
        mock_orchestration_runner.execute_financial_query.return_value = {
            'workflow_id': 'wf-progress-001',
            'status': 'completed',
            'progress': {
                'mysql_agent': {'status': 'completed', 'duration': 1.2},
                'shortage_agent': {'status': 'completed', 'duration': 2.1},
                'alert_agent': {'status': 'completed', 'duration': 0.8}
            },
            'results': {'mysql_analysis': {}, 'shortage_analysis': {}, 'alert_management': {}},
            'execution_time': 4.1
        }
        
        result = await mock_orchestration_runner.execute_financial_query(
            query="Test progress tracking"
        )
        
        # Validate progress tracking
        assert 'progress' in result, "Should include progress information"
        progress = result['progress']
        
        expected_agents = ['mysql_agent', 'shortage_agent', 'alert_agent']
        for agent in expected_agents:
            assert agent in progress, f"Should track progress for {agent}"
            assert 'success' in progress[agent], f"Should have success status for {agent}"
            assert 'duration' in progress[agent], f"Should have duration for {agent}"


class TestDataTransformationAccuracy:
    """Validate data transformation accuracy between agent handoffs."""

    @pytest.mark.asyncio
    async def test_mysql_to_shortage_data_transformation(
        self,
        context_manager: ContextManager,
        realistic_financial_scenarios: Dict[str, Any]
    ):
        """Test data transformation from MySQL agent to shortage analyzer."""
        scenario = realistic_financial_scenarios['critical_shortage']
        
        # Create mock MySQL context data
        mysql_context = MySQLContextData(
            query_results={
                'materials': scenario['materials'],
                'orders': scenario['orders'],
                'suppliers': scenario['suppliers']
            },
            extracted_entities={
                'materials': [m['code'] for m in scenario['materials']],
                'orders': [o['id'] for o in scenario['orders']]
            },
            execution_metadata={'query_time': '2024-11-01T10:00:00Z'}
        )
        
        # Test transformation to shortage analysis input format
        shortage_input = context_manager.transform_mysql_to_shortage(mysql_context)
        
        # Validate transformation accuracy
        assert 'materials' in shortage_input, "Should include materials data"
        assert 'orders' in shortage_input, "Should include orders data"
        
        # Validate material data transformation
        materials = shortage_input['materials']
        assert len(materials) == len(scenario['materials']), "Should preserve all materials"
        
        for i, material in enumerate(materials):
            original = scenario['materials'][i]
            assert material['code'] == original['code'], "Should preserve material code"
            assert 'current_stock' in material, "Should include stock information"

    @pytest.mark.asyncio
    async def test_shortage_to_alert_data_transformation(
        self,
        context_manager: ContextManager,
        realistic_financial_scenarios: Dict[str, Any]
    ):
        """Test data transformation from shortage analyzer to alert manager."""
        scenario = realistic_financial_scenarios['critical_shortage']
        
        # Create mock shortage context data
        shortage_context = ShortageContextData(
            shortage_analysis={
                'materials': [
                    {
                        'material_code': 'MM2004',
                        'shortage_index': 0.95,
                        'risk_level': 'HIGH',
                        'recommended_action': 'IMMEDIATE_ORDER'
                    }
                ],
                'overall_risk_score': 0.85,
                'priority_orders': ['CUSTORD-20241101001']
            },
            risk_assessments=[
                {'material': 'MM2004', 'risk': 'HIGH', 'impact': 'Critical shortage affects production'}
            ],
            recommendations=['ORDER_IMMEDIATELY', 'NOTIFY_MANAGEMENT']
        )
        
        # Test transformation to alert input format
        alert_input = context_manager.transform_shortage_to_alert(shortage_context)
        
        # Validate alert input format
        assert isinstance(alert_input, AlertManagementInputSchema), "Should be AlertManagementInputSchema"
        assert alert_input.shortage_analysis is not None, "Should include shortage analysis"
        assert alert_input.severity in ['LOW', 'MEDIUM', 'HIGH'], "Should have valid severity"
        
        # Validate data preservation
        shortage_data = alert_input.shortage_analysis
        assert 'materials' in shortage_data, "Should preserve materials analysis"
        assert 'overall_risk_score' in shortage_data, "Should preserve overall risk score"

    @pytest.mark.asyncio
    async def test_context_data_consistency_across_workflow(
        self,
        real_orchestration_runner: OrchestrationRunner,
        realistic_financial_queries: List[Dict[str, Any]]
    ):
        """Test that context data remains consistent across all agent handoffs."""
        query_data = realistic_financial_queries[0]  # shortage inquiry
        
        # Execute workflow and capture context transformations
        with patch.object(ContextManager, 'get_context_for_agent') as mock_get_context:
            # Mock context data for each agent
            mock_get_context.side_effect = [
                # MySQL agent context
                {'entities': ['CUSTORD-20241101001'], 'workflow_id': 'test-001'},
                # Shortage agent context (includes MySQL results)
                {
                    'mysql_analysis': {'materials': [{'code': 'MM2004'}]},
                    'entities': ['CUSTORD-20241101001'],
                    'workflow_id': 'test-001'
                },
                # Alert agent context (includes shortage results)
                {
                    'shortage_analysis': {'overall_risk_score': 0.7},
                    'mysql_analysis': {'materials': [{'code': 'MM2004'}]},
                    'workflow_id': 'test-001'
                }
            ]
            
            result = await real_orchestration_runner.execute_financial_query(
                query=query_data['query']
            )
            
            # Validate context consistency
            assert mock_get_context.call_count >= 2, "Should request context for multiple agents"
            
            # Validate workflow ID consistency across all calls
            for call in mock_get_context.call_args_list:
                context_data = call[0][1] if len(call[0]) > 1 else call.kwargs.get('context_data', {})
                if isinstance(context_data, dict) and 'workflow_id' in context_data:
                    assert context_data['workflow_id'] == 'test-001', "Workflow ID should be consistent"

    @pytest.mark.asyncio
    async def test_data_type_preservation_in_transformations(self):
        """Test that data types are preserved correctly during transformations."""
        # Test numeric data preservation
        mysql_data = MySQLContextData(
            query_results={
                'materials': [
                    {'code': 'MM2004', 'current_stock': 150, 'required': 200, 'unit_cost': 25.50}
                ]
            },
            extracted_entities={'materials': ['MM2004']},
            execution_metadata={'rows_affected': 1}
        )
        
        # Validate data types are preserved
        material = mysql_data.query_results['materials'][0]
        assert isinstance(material['current_stock'], int), "Stock should remain integer"
        assert isinstance(material['required'], int), "Required should remain integer"
        assert isinstance(material['unit_cost'], float), "Cost should remain float"
        assert isinstance(mysql_data.execution_metadata['rows_affected'], int), "Rows affected should remain integer"
        
        # Test shortage index calculations preserve precision
        shortage_data = ShortageContextData(
            shortage_analysis={
                'materials': [
                    {'material_code': 'MM2004', 'shortage_index': 0.75, 'risk_level': 'HIGH'}
                ],
                'overall_risk_score': 0.725
            },
            risk_assessments=[],
            recommendations=[]
        )
        
        material = shortage_data.shortage_analysis['materials'][0]
        assert isinstance(material['shortage_index'], float), "Shortage index should be float"
        assert 0.0 <= material['shortage_index'] <= 1.0, "Shortage index should be between 0 and 1"
        assert isinstance(shortage_data.shortage_analysis['overall_risk_score'], float), "Overall risk should be float"