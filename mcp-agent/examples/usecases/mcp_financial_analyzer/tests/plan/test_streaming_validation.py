"""Comprehensive streaming response validation and performance testing.

Tests real-time streaming functionality, latency compliance with <100ms requirements,
context passing during streaming, error handling in streaming operations, and
concurrent streaming workflows.
"""

import asyncio
import time
import pytest
import pytest_asyncio
from typing import Dict, Any, List, Optional, AsyncGenerator

# Add the parent directory to sys.path for imports
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))), Optional, AsyncGenerator
from unittest.mock import AsyncMock, patch, MagicMock
from contextlib import asynccontextmanager

from orchestrator.orchestration_runner import OrchestrationRunner
from orchestrator.financial_orchestrator import FinancialOrchestrator
from orchestrator.context_manager import ContextManager, FinancialWorkflowContext as WorkflowContext
from agents.base_agent_wrapper import BaseAgentWrapper as MySQLAgent
from agents.base_agent_wrapper import BaseAgentWrapper as ShortageAnalyzerAgent
from agents.alert_manager_agent import AlertManagerAgent


class StreamingTestHelper:
    """Helper class for streaming test utilities."""
    
    @staticmethod
    async def create_mock_streaming_generator(
        chunks: List[Dict[str, Any]], 
        chunk_delay: float = 0.05,
        simulate_errors: bool = False
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Create mock streaming data generator."""
        for i, chunk in enumerate(chunks):
            if simulate_errors and i == len(chunks) // 2:
                raise Exception(f"Simulated streaming error at chunk {i}")
            
            await asyncio.sleep(chunk_delay)
            yield chunk
    
    @staticmethod
    async def measure_streaming_latency(
        stream_generator: AsyncGenerator, 
        max_chunks: int = 100
    ) -> Dict[str, Any]:
        """Measure latency between streaming chunks."""
        latencies = []
        chunk_count = 0
        start_time = time.time()
        last_chunk_time = start_time
        
        try:
            async for chunk in stream_generator:
                current_time = time.time()
                if chunk_count > 0:  # Skip first chunk latency
                    latency_ms = (current_time - last_chunk_time) * 1000
                    latencies.append(latency_ms)
                
                last_chunk_time = current_time
                chunk_count += 1
                
                if chunk_count >= max_chunks:
                    break
                    
        except Exception as e:
            # Include error info in results
            pass
        
        total_time = time.time() - start_time
        
        return {
            'chunk_count': chunk_count,
            'latencies': latencies,
            'avg_latency': sum(latencies) / len(latencies) if latencies else 0,
            'max_latency': max(latencies) if latencies else 0,
            'min_latency': min(latencies) if latencies else 0,
            'total_time': total_time,
            'chunks_per_second': chunk_count / total_time if total_time > 0 else 0
        }


class TestStreamingResponseValidation:
    """Validate real-time streaming functionality across all workflow stages."""

    @pytest.fixture
    def mock_streaming_mysql_agent(self):
        """Create MySQL agent with streaming capabilities."""
        agent = AsyncMock(spec=MySQLAgent)
        
        # Mock streaming query results
        async def mock_streaming_query(query):
            chunks = [
                {'type': 'query_start', 'query': query, 'timestamp': time.time()},
                {'type': 'data', 'table': 'orders', 'rows': [{'id': 'CUSTORD-001', 'status': 'pending'}]},
                {'type': 'data', 'table': 'orders', 'rows': [{'id': 'CUSTORD-002', 'status': 'processing'}]},
                {'type': 'data', 'table': 'materials', 'rows': [{'code': 'MM2004', 'stock': 150}]},
                {'type': 'query_complete', 'total_rows': 3, 'execution_time': 0.8}
            ]
            
            return StreamingTestHelper.create_mock_streaming_generator(chunks)
        
        agent.stream_query = mock_streaming_query
        agent.process_query.return_value = {
            'status': 'success',
            'streaming': True,
            'data': {'orders': [], 'materials': []}
        }
        
        return agent

    @pytest.fixture
    def mock_streaming_shortage_agent(self):
        """Create shortage agent with streaming analysis capabilities."""
        agent = AsyncMock(spec=ShortageAnalyzerAgent)
        
        # Mock streaming shortage analysis
        async def mock_streaming_analysis(data):
            chunks = [
                {'type': 'analysis_start', 'materials_count': len(data.get('materials', []))},
                {'type': 'material_analysis', 'material': 'MM2004', 'shortage_index': 0.75, 'progress': 0.5},
                {'type': 'material_analysis', 'material': 'HCS500', 'shortage_index': 0.35, 'progress': 1.0},
                {'type': 'overall_analysis', 'overall_risk_score': 0.55, 'priority_orders': ['CUSTORD-001']},
                {'type': 'analysis_complete', 'recommendations': ['ORDER_MM2004_IMMEDIATELY']}
            ]
            
            return StreamingTestHelper.create_mock_streaming_generator(chunks)
        
        agent.stream_analysis = mock_streaming_analysis
        agent.analyze_shortage.return_value = {
            'status': 'success',
            'streaming': True,
            'shortage_analysis': {'overall_risk_score': 0.55}
        }
        
        return agent

    @pytest.fixture
    def mock_streaming_alert_agent(self):
        """Create alert agent with streaming notification capabilities."""
        agent = AsyncMock(spec=AlertManagerAgent)
        
        # Mock streaming alert processing
        async def mock_streaming_alerts(data):
            chunks = [
                {'type': 'alert_generation_start', 'alert_count': 2},
                {'type': 'alert_created', 'alert_id': 'ALT-001', 'severity': 'HIGH', 'type': 'SHORTAGE_WARNING'},
                {'type': 'notification_sent', 'alert_id': 'ALT-001', 'channel': 'email', 'status': 'sent'},
                {'type': 'notification_sent', 'alert_id': 'ALT-001', 'channel': 'slack', 'status': 'sent'},
                {'type': 'alert_processing_complete', 'total_sent': 2, 'failed': 0}
            ]
            
            return StreamingTestHelper.create_mock_streaming_generator(chunks)
        
        agent.stream_alerts = mock_streaming_alerts
        agent.send_alerts.return_value = {
            'status': 'success',
            'streaming': True,
            'alerts_sent': [{'alert_id': 'ALT-001', 'status': 'sent'}]
        }
        
        return agent

    @pytest.mark.asyncio
    async def test_mysql_agent_streaming_response(
        self,
        mock_streaming_mysql_agent,
        performance_monitor
    ):
        """Validate MySQL agent streaming responses and incremental data delivery."""
        performance_monitor.start_timing('mysql_streaming')
        
        # Test streaming query execution
        query = "SELECT * FROM orders WHERE status = 'pending'"
        stream = await mock_streaming_mysql_agent.stream_query(query)
        
        # Measure streaming performance
        latency_stats = await StreamingTestHelper.measure_streaming_latency(stream)
        
        streaming_time = performance_monitor.end_timing('mysql_streaming')
        
        # Validate streaming response
        assert latency_stats['chunk_count'] > 0, "Should receive streaming chunks"
        assert latency_stats['avg_latency'] < 100, f"Average latency {latency_stats['avg_latency']:.1f}ms should be under 100ms"
        assert latency_stats['max_latency'] < 200, f"Max latency {latency_stats['max_latency']:.1f}ms should be reasonable"
        
        # Validate streaming efficiency
        assert latency_stats['chunks_per_second'] > 10, "Should maintain reasonable streaming throughput"
        assert streaming_time < 2.0, f"Streaming should complete quickly, took {streaming_time:.2f}s"

    @pytest.mark.asyncio
    async def test_shortage_analysis_streaming_chunks(
        self,
        mock_streaming_shortage_agent,
        realistic_financial_scenarios: Dict[str, Any],
        performance_monitor
    ):
        """Test shortage analyzer streaming output and real-time index calculation."""
        scenario = realistic_financial_scenarios['critical_shortage']
        
        performance_monitor.start_timing('shortage_streaming')
        
        # Test streaming shortage analysis
        stream = await mock_streaming_shortage_agent.stream_analysis({
            'materials': scenario['materials'],
            'orders': scenario['orders']
        })
        
        # Collect streaming chunks
        chunks = []
        chunk_types = set()
        
        async for chunk in stream:
            chunks.append(chunk)
            chunk_types.add(chunk.get('type'))
        
        streaming_time = performance_monitor.end_timing('shortage_streaming')
        
        # Validate streaming chunk types
        expected_types = {'analysis_start', 'material_analysis', 'overall_analysis', 'analysis_complete'}
        assert chunk_types.intersection(expected_types), f"Should include expected chunk types: {expected_types}"
        
        # Validate material analysis streaming
        material_chunks = [c for c in chunks if c.get('type') == 'material_analysis']
        assert len(material_chunks) > 0, "Should stream material analysis results"
        
        for material_chunk in material_chunks:
            assert 'material' in material_chunk, "Material chunk should identify material"
            assert 'shortage_index' in material_chunk, "Should include shortage index"
            shortage_index = material_chunk['shortage_index']
            assert 0.0 <= shortage_index <= 1.0, f"Shortage index {shortage_index} should be in valid range"
        
        # Validate streaming performance
        assert streaming_time < 3.0, f"Shortage analysis streaming took too long: {streaming_time:.2f}s"

    @pytest.mark.asyncio
    async def test_alert_manager_streaming_notifications(
        self,
        mock_streaming_alert_agent,
        performance_monitor
    ):
        """Validate alert manager streaming notification delivery."""
        performance_monitor.start_timing('alert_streaming')
        
        # Test streaming alert processing
        alert_data = {
            'shortage_analysis': {
                'materials': [{'code': 'MM2004', 'shortage_index': 0.85, 'risk_level': 'HIGH'}],
                'overall_risk_score': 0.75
            },
            'channels': ['email', 'slack'],
            'severity': 'HIGH'
        }
        
        stream = await mock_streaming_alert_agent.stream_alerts(alert_data)
        
        # Track alert processing stages
        alert_stages = []
        notifications_sent = []
        
        async for chunk in stream:
            chunk_type = chunk.get('type')
            alert_stages.append(chunk_type)
            
            if chunk_type == 'notification_sent':
                notifications_sent.append(chunk)
        
        streaming_time = performance_monitor.end_timing('alert_streaming')
        
        # Validate alert streaming stages
        expected_stages = ['alert_generation_start', 'alert_created', 'notification_sent', 'alert_processing_complete']
        for stage in expected_stages:
            assert stage in alert_stages, f"Should include {stage} stage"
        
        # Validate notification streaming
        assert len(notifications_sent) > 0, "Should stream notification delivery status"
        
        for notification in notifications_sent:
            assert 'alert_id' in notification, "Notification should reference alert ID"
            assert 'channel' in notification, "Should specify notification channel"
            assert 'status' in notification, "Should include delivery status"
        
        # Validate streaming performance
        assert streaming_time < 2.0, f"Alert streaming took too long: {streaming_time:.2f}s"

    @pytest.mark.asyncio
    async def test_end_to_end_streaming_latency(
        self,
        mock_streaming_mysql_agent,
        mock_streaming_shortage_agent,
        mock_streaming_alert_agent,
        performance_monitor,
        streaming_latency_validator
    ):
        """Measure and validate <100ms streaming latency requirement across complete workflow."""
        performance_monitor.start_timing('e2e_streaming')
        
        # Create end-to-end streaming workflow
        async def e2e_streaming_workflow():
            # Stage 1: MySQL streaming
            mysql_stream = await mock_streaming_mysql_agent.stream_query("SELECT * FROM orders")
            async for chunk in mysql_stream:
                yield {'stage': 'mysql', 'data': chunk}
            
            # Stage 2: Shortage analysis streaming
            shortage_stream = await mock_streaming_shortage_agent.stream_analysis({'materials': []})
            async for chunk in shortage_stream:
                yield {'stage': 'shortage', 'data': chunk}
            
            # Stage 3: Alert streaming
            alert_stream = await mock_streaming_alert_agent.stream_alerts({'severity': 'HIGH'})
            async for chunk in alert_stream:
                yield {'stage': 'alert', 'data': chunk}
        
        # Validate end-to-end streaming latency
        e2e_stream = e2e_streaming_workflow()
        latency_stats = await streaming_latency_validator(e2e_stream, max_latency_ms=100)
        
        e2e_time = performance_monitor.end_timing('e2e_streaming')
        
        # Validate latency requirements
        assert latency_stats['avg_latency'] < 100, f"E2E average latency {latency_stats['avg_latency']:.1f}ms exceeds 100ms limit"
        assert latency_stats['max_latency'] < 150, f"E2E max latency {latency_stats['max_latency']:.1f}ms should be reasonable"
        
        # Validate workflow streaming performance
        assert e2e_time < 5.0, f"Complete streaming workflow took too long: {e2e_time:.2f}s"
        
        print(f"✓ E2E streaming: {latency_stats['chunk_count']} chunks, "
              f"avg latency: {latency_stats['avg_latency']:.1f}ms, "
              f"max latency: {latency_stats['max_latency']:.1f}ms")


class TestStreamingLatencyPerformance:
    """Test streaming latency compliance with <100ms requirements."""

    @pytest.fixture
    def latency_test_scenarios(self):
        """Provide test scenarios with different latency requirements."""
        return [
            {
                'name': 'low_latency',
                'chunk_count': 10,
                'chunk_delay': 0.02,  # 20ms between chunks
                'max_latency_ms': 50,
                'expected_avg_latency': 25
            },
            {
                'name': 'standard_latency',
                'chunk_count': 20,
                'chunk_delay': 0.05,  # 50ms between chunks
                'max_latency_ms': 100,
                'expected_avg_latency': 60
            },
            {
                'name': 'high_throughput',
                'chunk_count': 50,
                'chunk_delay': 0.01,  # 10ms between chunks
                'max_latency_ms': 30,
                'expected_avg_latency': 15
            }
        ]

    @pytest.mark.parametrize("scenario_name", ['low_latency', 'standard_latency', 'high_throughput'])
    @pytest.mark.asyncio
    async def test_streaming_latency_compliance(
        self,
        scenario_name: str,
        latency_test_scenarios: List[Dict[str, Any]],
        performance_monitor
    ):
        """Test streaming latency compliance for different scenarios."""
        scenario = next(s for s in latency_test_scenarios if s['name'] == scenario_name)
        
        performance_monitor.start_timing(f'latency_test_{scenario_name}')
        
        # Create test streaming data
        test_chunks = []
        for i in range(scenario['chunk_count']):
            test_chunks.append({
                'chunk_id': i,
                'data': f'test_data_{i}',
                'timestamp': time.time()
            })
        
        # Create streaming generator with specified delays
        stream = StreamingTestHelper.create_mock_streaming_generator(
            test_chunks, 
            chunk_delay=scenario['chunk_delay']
        )
        
        # Measure streaming latency
        latency_stats = await StreamingTestHelper.measure_streaming_latency(stream)
        
        test_time = performance_monitor.end_timing(f'latency_test_{scenario_name}')
        
        # Validate latency requirements
        max_latency_ms = scenario['max_latency_ms']
        assert latency_stats['max_latency'] < max_latency_ms, f"Max latency {latency_stats['max_latency']:.1f}ms exceeds limit {max_latency_ms}ms"
        
        # Validate average latency is reasonable
        expected_avg = scenario['expected_avg_latency']
        actual_avg = latency_stats['avg_latency']
        
        # Allow some variance (±20ms) due to system scheduling
        assert abs(actual_avg - expected_avg) < 20, f"Average latency {actual_avg:.1f}ms differs significantly from expected {expected_avg}ms"
        
        # Validate throughput
        expected_throughput = 1000 / scenario['chunk_delay']  # chunks per second
        actual_throughput = latency_stats['chunks_per_second']
        
        # Allow throughput to be within 50% of expected (system dependent)
        assert actual_throughput > expected_throughput * 0.5, f"Throughput {actual_throughput:.1f} too low, expected ~{expected_throughput:.1f}"
        
        print(f"✓ {scenario_name}: {latency_stats['chunk_count']} chunks, "
              f"avg: {actual_avg:.1f}ms, max: {latency_stats['max_latency']:.1f}ms, "
              f"throughput: {actual_throughput:.1f} chunks/s")

    @pytest.mark.asyncio
    async def test_streaming_under_load_conditions(
        self,
        performance_monitor,
        memory_usage_monitor
    ):
        """Test streaming latency under high load conditions."""
        memory_usage_monitor.start_monitoring()
        performance_monitor.start_timing('load_streaming')
        
        # Create high-load streaming scenario
        concurrent_streams = 5
        chunks_per_stream = 30
        
        async def create_load_stream(stream_id: int):
            chunks = []
            for i in range(chunks_per_stream):
                chunks.append({
                    'stream_id': stream_id,
                    'chunk_id': i,
                    'data': f'load_test_data_{stream_id}_{i}',
                    'payload_size': 1024  # 1KB payload
                })
            
            return StreamingTestHelper.create_mock_streaming_generator(chunks, chunk_delay=0.02)
        
        # Run concurrent streams
        streams = [await create_load_stream(i) for i in range(concurrent_streams)]
        
        # Measure latency for all streams concurrently
        latency_tasks = [
            StreamingTestHelper.measure_streaming_latency(stream) 
            for stream in streams
        ]
        
        latency_results = await asyncio.gather(*latency_tasks)
        
        load_test_time = performance_monitor.end_timing('load_streaming')
        memory_usage = memory_usage_monitor.get_memory_usage()
        
        # Validate load performance
        total_chunks = sum(result['chunk_count'] for result in latency_results)
        assert total_chunks == concurrent_streams * chunks_per_stream, "Should process all chunks"
        
        # Validate latency under load
        max_latencies = [result['max_latency'] for result in latency_results]
        avg_max_latency = sum(max_latencies) / len(max_latencies)
        
        assert avg_max_latency < 150, f"Average max latency under load {avg_max_latency:.1f}ms too high"
        
        # Validate memory usage doesn't grow excessively
        memory_usage_monitor.assert_memory_increase_under(100)  # Less than 100MB increase
        
        print(f"✓ Load test: {concurrent_streams} streams, {total_chunks} total chunks, "
              f"avg max latency: {avg_max_latency:.1f}ms, "
              f"memory increase: {memory_usage['increase_mb']:.1f}MB")

    @pytest.mark.asyncio
    async def test_streaming_latency_with_network_simulation(
        self,
        performance_monitor
    ):
        """Test streaming latency with simulated network conditions."""
        # Simulate different network conditions
        network_scenarios = [
            {'name': 'fast_network', 'delay': 0.001, 'jitter': 0.0001},  # 1ms ±0.1ms
            {'name': 'slow_network', 'delay': 0.05, 'jitter': 0.01},     # 50ms ±10ms
            {'name': 'unstable_network', 'delay': 0.02, 'jitter': 0.015} # 20ms ±15ms
        ]
        
        for scenario in network_scenarios:
            performance_monitor.start_timing(f'network_{scenario["name"]}')
            
            # Create streaming generator with network simulation
            async def network_simulated_stream():
                import random
                
                for i in range(20):
                    # Simulate network delay with jitter
                    base_delay = scenario['delay']
                    jitter = random.uniform(-scenario['jitter'], scenario['jitter'])
                    network_delay = max(0, base_delay + jitter)
                    
                    await asyncio.sleep(network_delay)
                    
                    yield {
                        'chunk_id': i,
                        'network_delay': network_delay,
                        'scenario': scenario['name']
                    }
            
            # Measure streaming performance
            stream = network_simulated_stream()
            latency_stats = await StreamingTestHelper.measure_streaming_latency(stream)
            
            test_time = performance_monitor.end_timing(f'network_{scenario["name"]}')
            
            # Validate network-specific performance
            base_delay_ms = scenario['delay'] * 1000
            expected_min_latency = base_delay_ms - (scenario['jitter'] * 1000)
            expected_max_latency = base_delay_ms + (scenario['jitter'] * 1000)
            
            print(f"✓ {scenario['name']}: avg latency {latency_stats['avg_latency']:.1f}ms, "
                  f"range: {latency_stats['min_latency']:.1f}-{latency_stats['max_latency']:.1f}ms")
            
            # Network latencies should be within expected ranges
            # (allowing for system overhead)
            assert latency_stats['min_latency'] >= expected_min_latency * 0.8, "Min latency too low"
            assert latency_stats['max_latency'] <= expected_max_latency * 1.5, "Max latency too high for network conditions"


class TestContextPassingInStreaming:
    """Ensure proper context sharing during streaming operations."""

    @pytest_asyncio.fixture
    async def streaming_context_manager(self):
        """Create context manager with streaming support."""
        manager = ContextManager()
        # Enable streaming context updates
        manager._streaming_enabled = True
        yield manager
        await manager.cleanup_all()

    @pytest.mark.asyncio
    async def test_streaming_context_consistency(
        self,
        streaming_context_manager: ContextManager,
        realistic_workflow_contexts: Dict[str, WorkflowContext]
    ):
        """Ensure context data remains consistent during streaming operations."""
        workflow_context = realistic_workflow_contexts['shortage_analysis']
        
        # Store initial context
        await streaming_context_manager.store_context(workflow_context)
        
        # Simulate streaming context updates
        async def streaming_context_updates():
            updates = [
                {'current_step': 'mysql_query', 'progress': 0.1, 'status': 'running'},
                {'current_step': 'mysql_query', 'progress': 0.5, 'status': 'processing_data'},
                {'current_step': 'shortage_analysis', 'progress': 0.7, 'status': 'calculating'},
                {'current_step': 'shortage_analysis', 'progress': 0.9, 'status': 'finalizing'},
                {'current_step': 'alert_generation', 'progress': 1.0, 'status': 'completed'}
            ]
            
            for i, update in enumerate(updates):
                await asyncio.sleep(0.02)  # 20ms between updates
                
                # Update context with streaming data
                updated_context = workflow_context.copy()
                updated_context.current_step = update['current_step']
                updated_context.metadata.update({
                    'progress': update['progress'],
                    'status': update['status'],
                    'last_update': time.time()
                })
                
                await streaming_context_manager.store_context(updated_context)
                
                yield {
                    'update_id': i,
                    'context_update': update,
                    'workflow_id': workflow_context.workflow_id
                }
        
        # Process streaming context updates
        context_states = []
        
        async for update_chunk in streaming_context_updates():
            # Retrieve context after each update
            current_context = await streaming_context_manager.get_context(
                workflow_context.workflow_id
            )
            
            context_states.append({
                'update_id': update_chunk['update_id'],
                'current_step': current_context.current_step,
                'progress': current_context.metadata.get('progress', 0),
                'status': current_context.metadata.get('status', 'unknown')
            })
        
        # Validate context consistency during streaming
        assert len(context_states) == 5, "Should track all context updates"
        
        # Validate progression
        for i, state in enumerate(context_states):
            assert state['progress'] == (i + 1) * 0.2, f"Progress should increase consistently at step {i}"
            assert state['update_id'] == i, f"Update ID should match sequence at step {i}"
        
        # Validate final state
        final_state = context_states[-1]
        assert final_state['current_step'] == 'alert_generation', "Should end at alert generation"
        assert final_state['progress'] == 1.0, "Should reach 100% progress"
        assert final_state['status'] == 'completed', "Should have completed status"

    @pytest.mark.asyncio
    async def test_concurrent_streaming_context_updates(
        self,
        streaming_context_manager: ContextManager
    ):
        """Test context updates from multiple streaming workflows."""
        # Create multiple workflows
        workflows = []
        for i in range(3):
            context = WorkflowContext(
                workflow_id=f'streaming-workflow-{i:02d}',
                workflow_type='shortage_analysis',
                user_query=f'Concurrent streaming test {i}',
                current_step='mysql_query',
                metadata={'progress': 0.0}
            )
            workflows.append(context)
            await streaming_context_manager.store_context(context)
        
        # Simulate concurrent streaming updates
        async def workflow_streaming_updates(workflow: WorkflowContext, workflow_index: int):
            steps = ['mysql_query', 'shortage_analysis', 'alert_generation']
            
            for step_index, step in enumerate(steps):
                await asyncio.sleep(0.01 * (workflow_index + 1))  # Staggered timing
                
                # Update workflow context
                updated_context = workflow.copy()
                updated_context.current_step = step
                updated_context.metadata.update({
                    'progress': (step_index + 1) / len(steps),
                    'last_update': time.time(),
                    'workflow_index': workflow_index
                })
                
                await streaming_context_manager.store_context(updated_context)
                
                yield {
                    'workflow_id': workflow.workflow_id,
                    'step': step,
                    'progress': updated_context.metadata['progress']
                }
        
        # Run concurrent streaming updates
        streaming_tasks = [
            workflow_streaming_updates(wf, i) 
            for i, wf in enumerate(workflows)
        ]
        
        # Collect all streaming updates
        all_updates = []
        
        async def collect_updates(stream_generator):
            updates = []
            async for update in stream_generator:
                updates.append(update)
            return updates
        
        concurrent_updates = await asyncio.gather(*[
            collect_updates(stream) for stream in streaming_tasks
        ])
        
        # Validate concurrent streaming results
        for i, workflow_updates in enumerate(concurrent_updates):
            assert len(workflow_updates) == 3, f"Workflow {i} should have 3 step updates"
            
            # Check final context state
            final_context = await streaming_context_manager.get_context(workflows[i].workflow_id)
            assert final_context.current_step == 'alert_generation', f"Workflow {i} should end at alert generation"
            assert final_context.metadata['progress'] == 1.0, f"Workflow {i} should reach 100% progress"

    @pytest.mark.asyncio
    async def test_streaming_context_error_recovery(
        self,
        streaming_context_manager: ContextManager
    ):
        """Test context recovery when streaming operations encounter errors."""
        # Create workflow with streaming context
        workflow_context = WorkflowContext(
            workflow_id='streaming-error-test',
            workflow_type='shortage_analysis',
            user_query='Test streaming error recovery',
            current_step='mysql_query'
        )
        
        await streaming_context_manager.store_context(workflow_context)
        
        # Simulate streaming with errors
        async def streaming_with_errors():
            updates = [
                {'step': 'mysql_query', 'progress': 0.3, 'status': 'running'},
                {'step': 'mysql_query', 'progress': 0.6, 'status': 'processing'},
                None,  # Simulate context update failure
                {'step': 'shortage_analysis', 'progress': 0.8, 'status': 'calculating'},
                {'step': 'shortage_analysis', 'progress': 1.0, 'status': 'completed'}
            ]
            
            for i, update in enumerate(updates):
                await asyncio.sleep(0.01)
                
                if update is None:
                    # Simulate context update error
                    raise Exception("Simulated context update failure")
                
                # Update context
                updated_context = workflow_context.copy()
                updated_context.current_step = update['step']
                updated_context.metadata.update({
                    'progress': update['progress'],
                    'status': update['status']
                })
                
                await streaming_context_manager.store_context(updated_context)
                
                yield update
        
        # Process streaming with error handling
        successful_updates = []
        error_occurred = False
        
        try:
            async for update in streaming_with_errors():
                successful_updates.append(update)
        except Exception as e:
            error_occurred = True
            print(f"Streaming error handled: {str(e)}")
        
        # Validate error recovery
        assert error_occurred, "Should have encountered simulated error"
        assert len(successful_updates) == 2, "Should have processed updates before error"
        
        # Context should still be retrievable and consistent
        recovered_context = await streaming_context_manager.get_context('streaming-error-test')
        assert recovered_context is not None, "Context should be recoverable after streaming error"
        
        # Should have last successful update
        last_update = successful_updates[-1]
        assert recovered_context.current_step == last_update['step'], "Should preserve last successful step"
        assert recovered_context.metadata['progress'] == last_update['progress'], "Should preserve last successful progress"


class TestStreamingErrorHandling:
    """Test error handling during streaming operations."""

    @pytest.mark.asyncio
    async def test_streaming_interruption_recovery(
        self,
        performance_monitor
    ):
        """Test recovery mechanisms when streaming is interrupted."""
        performance_monitor.start_timing('interruption_recovery')
        
        # Create streaming generator that gets interrupted
        async def interruptible_stream():
            for i in range(10):
                if i == 5:
                    # Simulate network interruption
                    raise ConnectionError("Network connection lost during streaming")
                
                await asyncio.sleep(0.01)
                yield {'chunk_id': i, 'data': f'chunk_{i}'}
        
        # Test interruption handling with retry
        max_retries = 3
        successful_chunks = []
        
        for retry_count in range(max_retries):
            try:
                stream = interruptible_stream()
                async for chunk in stream:
                    successful_chunks.append(chunk)
                break  # Success
                
            except ConnectionError as e:
                print(f"Streaming interrupted on retry {retry_count}: {str(e)}")
                
                if retry_count < max_retries - 1:
                    # Implement exponential backoff
                    await asyncio.sleep(0.1 * (2 ** retry_count))
                    continue
                else:
                    # Final failure
                    print("Max retries reached, implementing recovery strategy")
                    break
        
        recovery_time = performance_monitor.end_timing('interruption_recovery')
        
        # Validate interruption handling
        assert len(successful_chunks) > 0, "Should have received some chunks before interruption"
        assert len(successful_chunks) < 10, "Should not have received all chunks due to interruption"
        assert recovery_time < 5.0, f"Recovery should complete quickly, took {recovery_time:.2f}s"

    @pytest.mark.asyncio
    async def test_streaming_partial_failure_handling(
        self,
        mock_streaming_mysql_agent,
        mock_streaming_shortage_agent
    ):
        """Test handling when some streaming agents fail while others succeed."""
        # Mock MySQL streaming success
        mysql_success = True
        mysql_chunks = []
        
        mysql_stream = await mock_streaming_mysql_agent.stream_query("SELECT * FROM orders")
        try:
            async for chunk in mysql_stream:
                mysql_chunks.append(chunk)
        except Exception:
            mysql_success = False
        
        # Mock shortage streaming failure
        shortage_success = True
        shortage_chunks = []
        
        # Modify shortage agent to fail mid-stream
        async def failing_shortage_stream(data):
            chunks = [
                {'type': 'analysis_start', 'materials_count': 2},
                {'type': 'material_analysis', 'material': 'MM2004', 'progress': 0.5}
            ]
            
            for i, chunk in enumerate(chunks):
                if i == 1:  # Fail on second chunk
                    raise Exception("Shortage analysis service overloaded")
                await asyncio.sleep(0.01)
                yield chunk
        
        mock_streaming_shortage_agent.stream_analysis = failing_shortage_stream
        
        shortage_stream = await mock_streaming_shortage_agent.stream_analysis({'materials': []})
        try:
            async for chunk in shortage_stream:
                shortage_chunks.append(chunk)
        except Exception as e:
            shortage_success = False
            print(f"Shortage streaming failed: {str(e)}")
        
        # Validate partial failure handling
        assert mysql_success, "MySQL streaming should succeed"
        assert len(mysql_chunks) > 0, "Should receive MySQL streaming data"
        
        assert not shortage_success, "Shortage streaming should fail"
        assert len(shortage_chunks) >= 1, "Should receive some shortage chunks before failure"

    @pytest.mark.asyncio
    async def test_streaming_timeout_handling(
        self,
        performance_monitor
    ):
        """Test streaming behavior under timeout conditions."""
        performance_monitor.start_timing('streaming_timeout')
        
        # Create slow streaming generator
        async def slow_stream():
            for i in range(5):
                if i == 2:
                    # Simulate very slow chunk (timeout scenario)
                    await asyncio.sleep(0.5)  # 500ms delay
                else:
                    await asyncio.sleep(0.01)  # Normal delay
                
                yield {'chunk_id': i, 'delay_ms': 500 if i == 2 else 10}
        
        # Test with timeout protection
        timeout_seconds = 0.2  # 200ms timeout
        chunks_received = []
        timeout_occurred = False
        
        try:
            stream = slow_stream()
            async for chunk in asyncio.wait_for(stream, timeout=timeout_seconds):
                chunks_received.append(chunk)
                
                # Individual chunk timeout check
                if chunk.get('delay_ms', 0) > timeout_seconds * 1000:
                    timeout_occurred = True
                    break
                    
        except asyncio.TimeoutError:
            timeout_occurred = True
            print("Streaming timeout occurred as expected")
        
        streaming_time = performance_monitor.end_timing('streaming_timeout')
        
        # Validate timeout handling
        # Note: Due to asyncio implementation, we might receive some chunks before timeout
        assert len(chunks_received) <= 3, "Should not receive all chunks due to timeout"
        assert streaming_time < 1.0, "Should timeout within reasonable time"


class TestConcurrentStreamingWorkflows:
    """Test multiple concurrent streaming workflows."""

    @pytest.mark.asyncio
    async def test_multiple_concurrent_streaming_workflows(
        self,
        performance_monitor,
        memory_usage_monitor
    ):
        """Execute multiple streaming workflows concurrently and validate performance."""
        memory_usage_monitor.start_monitoring()
        performance_monitor.start_timing('concurrent_streaming_workflows')
        
        # Create multiple streaming workflows
        workflow_count = 4
        
        async def create_workflow_stream(workflow_id: int):
            """Create a complete workflow stream."""
            stages = ['mysql', 'shortage', 'alert']
            
            for stage_idx, stage in enumerate(stages):
                for chunk_idx in range(5):  # 5 chunks per stage
                    await asyncio.sleep(0.005)  # 5ms per chunk
                    
                    yield {
                        'workflow_id': workflow_id,
                        'stage': stage,
                        'chunk_id': chunk_idx,
                        'progress': ((stage_idx * 5) + chunk_idx + 1) / 15,
                        'timestamp': time.time()
                    }
        
        # Execute concurrent streaming workflows
        workflow_streams = [create_workflow_stream(i) for i in range(workflow_count)]
        
        # Process all streams concurrently
        workflow_results = []
        
        async def process_workflow_stream(stream, workflow_id):
            chunks = []
            start_time = time.time()
            
            async for chunk in stream:
                chunks.append(chunk)
            
            end_time = time.time()
            return {
                'workflow_id': workflow_id,
                'chunk_count': len(chunks),
                'duration': end_time - start_time,
                'chunks_per_second': len(chunks) / (end_time - start_time) if end_time > start_time else 0
            }
        
        # Run all workflows concurrently
        workflow_tasks = [
            process_workflow_stream(stream, i) 
            for i, stream in enumerate(workflow_streams)
        ]
        
        workflow_results = await asyncio.gather(*workflow_tasks)
        
        total_time = performance_monitor.end_timing('concurrent_streaming_workflows')
        memory_usage = memory_usage_monitor.get_memory_usage()
        
        # Validate concurrent streaming performance
        assert len(workflow_results) == workflow_count, f"Should complete all {workflow_count} workflows"
        
        total_chunks = sum(result['chunk_count'] for result in workflow_results)
        expected_chunks = workflow_count * 3 * 5  # 4 workflows * 3 stages * 5 chunks
        assert total_chunks == expected_chunks, f"Should process {expected_chunks} chunks, got {total_chunks}"
        
        # Validate individual workflow performance
        for result in workflow_results:
            assert result['chunk_count'] == 15, f"Workflow {result['workflow_id']} should have 15 chunks"
            assert result['chunks_per_second'] > 100, f"Workflow {result['workflow_id']} throughput too low: {result['chunks_per_second']:.1f}"
        
        # Validate overall performance
        overall_throughput = total_chunks / total_time
        assert overall_throughput > 200, f"Overall throughput too low: {overall_throughput:.1f} chunks/s"
        
        # Validate memory usage
        memory_usage_monitor.assert_memory_increase_under(50)  # Less than 50MB for concurrent streaming
        
        print(f"✓ Concurrent streaming: {workflow_count} workflows, {total_chunks} chunks, "
              f"throughput: {overall_throughput:.1f} chunks/s, "
              f"memory: {memory_usage['increase_mb']:.1f}MB")

    @pytest.mark.asyncio
    async def test_streaming_workflow_isolation(
        self,
        performance_monitor
    ):
        """Ensure streaming workflows don't interfere with each other."""
        performance_monitor.start_timing('workflow_isolation')
        
        # Create workflows with different characteristics
        workflows = [
            {'id': 0, 'chunk_delay': 0.01, 'error_chunk': None},      # Fast, no errors
            {'id': 1, 'chunk_delay': 0.05, 'error_chunk': None},      # Slow, no errors
            {'id': 2, 'chunk_delay': 0.02, 'error_chunk': 5},         # Medium, with error
            {'id': 3, 'chunk_delay': 0.01, 'error_chunk': None}       # Fast, no errors
        ]
        
        async def create_isolated_workflow_stream(config):
            """Create workflow stream with specific characteristics."""
            for i in range(10):
                if config['error_chunk'] is not None and i == config['error_chunk']:
                    raise ValueError(f"Simulated error in workflow {config['id']} at chunk {i}")
                
                await asyncio.sleep(config['chunk_delay'])
                yield {
                    'workflow_id': config['id'],
                    'chunk_id': i,
                    'timestamp': time.time()
                }
        
        # Process workflows and track results
        workflow_results = {}
        
        async def process_isolated_workflow(config):
            chunks = []
            error = None
            
            try:
                stream = create_isolated_workflow_stream(config)
                async for chunk in stream:
                    chunks.append(chunk)
            except Exception as e:
                error = str(e)
            
            return {
                'workflow_id': config['id'],
                'chunks_received': len(chunks),
                'error': error,
                'config': config
            }
        
        # Run workflows concurrently
        isolation_tasks = [process_isolated_workflow(config) for config in workflows]
        results = await asyncio.gather(*isolation_tasks)
        
        isolation_time = performance_monitor.end_timing('workflow_isolation')
        
        # Validate workflow isolation
        for result in results:
            workflow_id = result['workflow_id']
            config = result['config']
            
            if config['error_chunk'] is not None:
                # Workflow with error should fail but not affect others
                assert result['error'] is not None, f"Workflow {workflow_id} should have error"
                assert result['chunks_received'] == config['error_chunk'], f"Workflow {workflow_id} should stop at error chunk"
            else:
                # Workflow without error should complete successfully
                assert result['error'] is None, f"Workflow {workflow_id} should not have error"
                assert result['chunks_received'] == 10, f"Workflow {workflow_id} should receive all chunks"
        
        # Validate that error in one workflow didn't affect others
        successful_workflows = [r for r in results if r['error'] is None]
        assert len(successful_workflows) == 3, "Should have 3 successful workflows despite 1 failing"
        
        print(f"✓ Workflow isolation: {len(results)} workflows, "
              f"{len(successful_workflows)} successful, 1 failed as expected")

    @pytest.mark.asyncio
    async def test_streaming_resource_management(
        self,
        memory_usage_monitor,
        performance_monitor
    ):
        """Test resource management during high-volume streaming."""
        memory_usage_monitor.start_monitoring()
        performance_monitor.start_timing('resource_management')
        
        # Create resource-intensive streaming scenario
        stream_count = 6
        chunks_per_stream = 100
        
        async def create_resource_intensive_stream(stream_id: int):
            """Create stream with larger data payloads."""
            for i in range(chunks_per_stream):
                # Create larger payload to test memory management
                payload = {
                    'stream_id': stream_id,
                    'chunk_id': i,
                    'large_data': 'x' * 1024,  # 1KB payload per chunk
                    'metadata': {
                        'processing_stats': {'cpu': 0.1, 'memory': 0.05},
                        'timestamp': time.time(),
                        'checksum': hash(f'{stream_id}_{i}')
                    }
                }
                
                await asyncio.sleep(0.001)  # 1ms delay
                yield payload
        
        # Process streams with resource monitoring
        processed_chunks = 0
        max_concurrent_chunks = 0
        current_chunks_in_memory = 0
        
        async def process_resource_stream(stream_id):
            nonlocal processed_chunks, current_chunks_in_memory, max_concurrent_chunks
            
            chunks_processed = 0
            stream = create_resource_intensive_stream(stream_id)
            
            async for chunk in stream:
                current_chunks_in_memory += 1
                max_concurrent_chunks = max(max_concurrent_chunks, current_chunks_in_memory)
                
                # Simulate chunk processing
                await asyncio.sleep(0.001)
                
                chunks_processed += 1
                processed_chunks += 1
                
                # Simulate chunk cleanup
                current_chunks_in_memory -= 1
                
                # Update memory monitoring
                memory_usage_monitor.update_peak()
            
            return chunks_processed
        
        # Run resource-intensive streams
        resource_tasks = [process_resource_stream(i) for i in range(stream_count)]
        chunk_counts = await asyncio.gather(*resource_tasks)
        
        resource_time = performance_monitor.end_timing('resource_management')
        final_memory = memory_usage_monitor.get_memory_usage()
        
        # Validate resource management
        total_expected_chunks = stream_count * chunks_per_stream
        assert processed_chunks == total_expected_chunks, f"Should process {total_expected_chunks} chunks, got {processed_chunks}"
        
        # Validate memory management
        expected_data_size = total_expected_chunks * 1  # 1KB per chunk
        memory_increase_mb = final_memory['increase_mb']
        
        # Memory usage should be reasonable (allow for overhead)
        assert memory_increase_mb < expected_data_size * 2, f"Memory usage {memory_increase_mb}MB too high for {expected_data_size}KB data"
        
        # Validate concurrent chunk management
        assert max_concurrent_chunks < stream_count * 10, "Should not hold too many chunks in memory concurrently"
        
        # Validate processing performance
        throughput = processed_chunks / resource_time
        assert throughput > 500, f"Resource management throughput too low: {throughput:.1f} chunks/s"
        
        print(f"✓ Resource management: {stream_count} streams, {processed_chunks} chunks, "
              f"memory: {memory_increase_mb:.1f}MB, throughput: {throughput:.1f} chunks/s, "
              f"max concurrent: {max_concurrent_chunks} chunks")