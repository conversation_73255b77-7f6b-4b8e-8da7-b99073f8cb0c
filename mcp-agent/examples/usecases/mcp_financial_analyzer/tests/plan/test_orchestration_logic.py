"""Comprehensive orchestration logic testing focusing on query interpretation, agent routing, and coordination.

Tests FinancialQueryProcessor query classification, WorkflowExecutor coordination, ContextManager sharing,
concurrent execution handling, and workflow pattern execution validation.
"""

import asyncio
import pytest
import pytest_asyncio
from typing import Dict, Any, List, Optional

# Add the parent directory to sys.path for imports
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))), Optional
from unittest.mock import AsyncMock, patch, MagicMock

from orchestrator.financial_orchestrator import FinancialOrchestrator
from orchestrator.query_processor import FinancialQueryProcessor
from orchestrator.orchestration_runner import OrchestrationRunner
from orchestrator.context_manager import ContextManager, FinancialWorkflowContext as WorkflowContext
from orchestrator.workflow_patterns import (
    WorkflowPatternRegistry,
    WorkflowPattern,
    WorkflowExecutor,
    ExecutionMode
)
from orchestrator.query_processor import QueryType
from orchestrator.workflow_patterns import WorkflowStep
# Define MCPOrchestratorInputSchema if not available
try:
    from agents.mysql_agent import MCPOrchestratorInputSchema
except ImportError:
    from pydantic import BaseModel

    class MCPOrchestratorInputSchema(BaseModel):
        query: str
        entities: Dict[str, Any] = {}
        parameters: Dict[str, Any] = {}
from orchestrator.context_manager import MySQLContextData, ShortageContextData, AlertContextData


class TestQueryInterpretationAndRouting:
    """Validate FinancialQueryProcessor correctly classifies queries and selects appropriate workflow patterns."""

    @pytest.fixture
    def query_processor(self):
        """Create FinancialQueryProcessor instance for testing."""
        return FinancialQueryProcessor()

    @pytest.fixture
    def workflow_registry(self):
        """Create WorkflowPatternRegistry instance for testing."""
        return WorkflowPatternRegistry()

    @pytest.mark.asyncio
    async def test_query_classification_accuracy(
        self, 
        query_processor: FinancialQueryProcessor,
        realistic_financial_queries: List[Dict[str, Any]]
    ):
        """Test FinancialQueryProcessor.process_query() with various financial queries and validate QueryType classification."""
        for query_data in realistic_financial_queries:
            query = query_data['query']
            expected_type = query_data['query_type']
            
            # Process the query
            result = await query_processor.process_query(query)
            
            # Validate query processing results
            assert result is not None, f"Query processing should return results for: {query}"
            assert 'query_type' in result, f"Should classify query type for: {query}"
            assert 'confidence' in result, f"Should provide confidence score for: {query}"
            assert 'extracted_entities' in result, f"Should extract entities for: {query}"
            
            # Validate classification accuracy
            classified_type = result['query_type']
            assert classified_type is not None, f"Should classify query type for: {query}"
            
            # Validate confidence score
            confidence = result['confidence']
            assert isinstance(confidence, (int, float)), "Confidence should be numeric"
            assert 0.0 <= confidence <= 1.0, "Confidence should be between 0 and 1"

    @pytest.mark.asyncio
    async def test_entity_extraction_validation(
        self,
        query_processor: FinancialQueryProcessor
    ):
        """Verify extraction of orders (CUSTORD-YYYYMMXXX), materials, suppliers, and other entities."""
        test_queries = [
            {
                'query': 'Check status of order CUSTORD-20241101001 and material MM2004',
                'expected_entities': {
                    'orders': ['CUSTORD-20241101001'],
                    'materials': ['MM2004']
                }
            },
            {
                'query': 'Analyze shortage for HCS500, DDR5_32GB and orders WO-20241102001, PR-20241103001',
                'expected_entities': {
                    'materials': ['HCS500', 'DDR5_32GB'],
                    'orders': ['WO-20241102001', 'PR-20241103001']
                }
            },
            {
                'query': 'Evaluate supplier SteelWorks Ltd reliability for material SSD-512GB',
                'expected_entities': {
                    'suppliers': ['SteelWorks Ltd'],
                    'materials': ['SSD-512GB']
                }
            }
        ]
        
        for test_case in test_queries:
            result = await query_processor.process_query(test_case['query'])
            
            # Validate entity extraction
            extracted = result['extracted_entities']
            expected = test_case['expected_entities']
            
            for entity_type, expected_values in expected.items():
                assert entity_type in extracted, f"Should extract {entity_type} from query: {test_case['query']}"
                extracted_values = extracted[entity_type]
                
                for expected_value in expected_values:
                    assert expected_value in extracted_values, f"Should extract {expected_value} from query: {test_case['query']}"

    @pytest.mark.asyncio
    async def test_workflow_pattern_selection(
        self,
        workflow_registry: WorkflowPatternRegistry,
        realistic_financial_queries: List[Dict[str, Any]]
    ):
        """Validate WorkflowPatternRegistry.get_pattern_for_query_type() selects appropriate patterns."""
        for query_data in realistic_financial_queries:
            expected_workflow = query_data['expected_workflow']
            query_type = query_data['query_type']
            
            # Get workflow pattern for query type
            pattern = workflow_registry.get_pattern_for_query_type(query_type)
            
            # Validate pattern selection
            assert pattern is not None, f"Should return pattern for query type: {query_type}"
            assert isinstance(pattern, WorkflowPattern), f"Should return WorkflowPattern instance"
            
            # Validate pattern properties
            assert pattern.name is not None, f"Pattern should have name"
            assert pattern.steps is not None, f"Pattern should have steps"
            assert len(pattern.steps) > 0, f"Pattern should have at least one step"
            
            # Validate workflow pattern matches expectation
            if hasattr(pattern, 'workflow_type'):
                assert pattern.workflow_type == expected_workflow, f"Pattern workflow type should match expected: {expected_workflow}"

    @pytest.mark.asyncio
    async def test_query_confidence_scoring_and_ambiguity_detection(
        self,
        query_processor: FinancialQueryProcessor
    ):
        """Test query confidence scoring and ambiguity detection."""
        test_queries = [
            {
                'query': 'What is the shortage status for order CUSTORD-20241101001?',
                'expected_confidence': 'high',  # Clear, specific query
                'expected_ambiguity': False
            },
            {
                'query': 'Check materials',
                'expected_confidence': 'low',   # Vague query
                'expected_ambiguity': True
            },
            {
                'query': 'Analyze supplier reliability for materials MM2004 and HCS500',
                'expected_confidence': 'high',  # Specific entities and action
                'expected_ambiguity': False
            },
            {
                'query': 'What is the status?',
                'expected_confidence': 'low',   # Very vague
                'expected_ambiguity': True
            }
        ]
        
        for test_case in test_queries:
            result = await query_processor.process_query(test_case['query'])
            
            confidence = result['confidence']
            
            # Validate confidence levels
            if test_case['expected_confidence'] == 'high':
                assert confidence > 0.7, f"High confidence query should score > 0.7: {test_case['query']}"
            elif test_case['expected_confidence'] == 'low':
                assert confidence < 0.5, f"Low confidence query should score < 0.5: {test_case['query']}"
            
            # Validate ambiguity detection
            is_ambiguous = result.get('ambiguous', False)
            assert is_ambiguous == test_case['expected_ambiguity'], f"Ambiguity detection failed for: {test_case['query']}"

    @pytest.mark.asyncio
    async def test_agent_routing_decisions(
        self,
        realistic_financial_queries: List[Dict[str, Any]]
    ):
        """Test routing logic in FinancialOrchestrator.analyze_financial_query()."""
        orchestrator = FinancialOrchestrator()
        
        for query_data in realistic_financial_queries:
            query = query_data['query']
            expected_workflow = query_data['expected_workflow']
            
            # Analyze financial query
            analysis_result = await orchestrator.analyze_financial_query(query)
            
            # Validate routing decision
            assert analysis_result is not None, f"Should return analysis for: {query}"
            assert 'workflow_type' in analysis_result, f"Should determine workflow type for: {query}"
            assert 'agent_sequence' in analysis_result, f"Should determine agent sequence for: {query}"
            
            workflow_type = analysis_result['workflow_type']
            agent_sequence = analysis_result['agent_sequence']
            
            # Validate workflow type selection
            assert workflow_type == expected_workflow, f"Should route to {expected_workflow} for: {query}"
            
            # Validate agent sequence
            assert isinstance(agent_sequence, list), "Agent sequence should be list"
            assert len(agent_sequence) > 0, "Should have at least one agent in sequence"
            
            # Validate typical agent sequence for financial workflows
            expected_agents = ['mysql', 'shortage', 'alert']
            for agent in agent_sequence:
                assert agent in expected_agents, f"Agent {agent} should be in expected agents list"


class TestAgentCoordinationAndSequencing:
    """Test WorkflowExecutor properly coordinates agent execution order."""

    @pytest.fixture
    def workflow_executor(self):
        """Create WorkflowExecutor instance for testing."""
        return WorkflowExecutor()

    @pytest.fixture
    def sample_workflow_pattern(self):
        """Create sample workflow pattern for testing."""
        return WorkflowPattern(
            pattern_id="test_shortage_analysis",
            name="test_shortage_analysis",
            description="Test shortage analysis workflow",
            steps=[
                WorkflowStep(
                    step_id="mysql_query",
                    agent_name="mysql",
                    description="Execute MySQL query step",
                    dependencies=[],
                    optional=False,
                    timeout=60
                ),
                WorkflowStep(
                    step_id="shortage_analysis",
                    agent_name="shortage",
                    description="Perform shortage analysis",
                    dependencies=["mysql_query"],
                    optional=False,
                    timeout=120
                ),
                WorkflowStep(
                    step_id="alert_generation",
                    agent_name="alert",
                    description="Generate alerts if needed",
                    dependencies=["shortage_analysis"],
                    optional=True,
                    timeout=30
                )
            ],
            execution_mode=ExecutionMode.SEQUENTIAL,
            estimated_duration=210
        )

    @pytest.mark.asyncio
    async def test_workflow_step_dependency_resolution(
        self,
        workflow_executor: WorkflowExecutor,
        sample_workflow_pattern: WorkflowPattern
    ):
        """Validate WorkflowStep.can_execute() and dependency management."""
        steps = sample_workflow_pattern.steps
        completed_steps = set()
        
        # Test initial state - only steps without dependencies can execute
        mysql_step = steps[0]  # No dependencies
        shortage_step = steps[1]  # Depends on mysql_query
        alert_step = steps[2]  # Depends on shortage_analysis
        
        # Initially, only MySQL step should be executable
        assert mysql_step.can_execute(completed_steps), "MySQL step should be executable initially"
        assert not shortage_step.can_execute(completed_steps), "Shortage step should wait for MySQL"
        assert not alert_step.can_execute(completed_steps), "Alert step should wait for shortage"
        
        # After MySQL completion
        completed_steps.add("mysql_query")
        assert shortage_step.can_execute(completed_steps), "Shortage step should be executable after MySQL"
        assert not alert_step.can_execute(completed_steps), "Alert step should still wait for shortage"
        
        # After shortage completion
        completed_steps.add("shortage_analysis")
        assert alert_step.can_execute(completed_steps), "Alert step should be executable after shortage"

    @pytest.mark.asyncio
    async def test_sequential_execution_mode(
        self,
        workflow_executor: WorkflowExecutor,
        sample_workflow_pattern: WorkflowPattern
    ):
        """Test ExecutionMode.SEQUENTIAL workflow execution."""
        # Mock agent execution
        with patch.object(workflow_executor, 'execute_agent_step') as mock_execute:
            mock_execute.return_value = {'status': 'success', 'results': {}}
            
            # Execute workflow in sequential mode
            result = await workflow_executor.execute_workflow(
                pattern=sample_workflow_pattern,
                context=WorkflowContext(
                    workflow_id='test-seq-001',
                    user_query='Test sequential execution'
                )
            )
            
            # Validate execution
            assert result is not None, "Sequential execution should return results"
            assert mock_execute.call_count == 3, "Should execute all 3 steps"
            
            # Validate execution order (sequential)
            calls = mock_execute.call_args_list
            step_names = [call[0][0].step_id for call in calls]
            expected_order = ['mysql_query', 'shortage_analysis', 'alert_generation']
            assert step_names == expected_order, f"Should execute in order: {expected_order}"

    @pytest.mark.asyncio
    async def test_parallel_execution_mode(
        self,
        workflow_executor: WorkflowExecutor
    ):
        """Test ExecutionMode.PARALLEL workflow execution."""
        # Create workflow pattern with parallel steps
        parallel_pattern = WorkflowPattern(
            pattern_id="test_parallel_analysis",
            name="test_parallel_analysis", 
            description="Test parallel analysis workflow",
            steps=[
                WorkflowStep(step_id="mysql_query", agent_name="mysql", description="MySQL query", dependencies=[], optional=False),
                WorkflowStep(step_id="shortage_analysis", agent_name="shortage", description="Shortage analysis", dependencies=["mysql_query"], optional=False),
                WorkflowStep(step_id="supplier_analysis", agent_name="supplier", description="Supplier analysis", dependencies=["mysql_query"], optional=False),
                WorkflowStep(step_id="alert_generation", agent_name="alert", description="Alert generation", dependencies=["shortage_analysis", "supplier_analysis"], optional=True)
            ],
            execution_mode=ExecutionMode.PARALLEL,
            estimated_duration=150
        )
        
        # Mock agent execution with delays to test parallelism
        execution_times = []
        
        async def mock_execute_with_timing(step):
            import time
            start_time = time.time()
            await asyncio.sleep(0.1)  # Simulate work
            execution_times.append((step.step_id, time.time() - start_time))
            return {'status': 'success', 'results': {}}
        
        with patch.object(workflow_executor, 'execute_agent_step', side_effect=mock_execute_with_timing):
            result = await workflow_executor.execute_workflow(
                pattern=parallel_pattern,
                context=WorkflowContext(
                    workflow_id='test-par-001',
                    workflow_type='comprehensive',
                    user_query='Test parallel execution'
                )
            )
            
            # Validate parallel execution
            assert result is not None, "Parallel execution should return results"
            
            # Validate that shortage and supplier analysis ran in parallel
            # (both should start after mysql completes, but run concurrently)
            step_times = {name: time for name, time in execution_times}
            assert 'shortage_analysis' in step_times, "Should execute shortage analysis"
            assert 'supplier_analysis' in step_times, "Should execute supplier analysis"

    @pytest.mark.asyncio
    async def test_hybrid_execution_mode(
        self,
        workflow_executor: WorkflowExecutor
    ):
        """Test ExecutionMode.HYBRID workflow execution."""
        # Create workflow pattern with hybrid execution
        hybrid_pattern = WorkflowPattern(
            pattern_id="test_hybrid_analysis",
            name="test_hybrid_analysis",
            description="Test hybrid analysis workflow",
            steps=[
                WorkflowStep(step_id="mysql_query", agent_name="mysql", description="MySQL query", dependencies=[], optional=False),
                WorkflowStep(step_id="shortage_analysis", agent_name="shortage", description="Shortage analysis", dependencies=["mysql_query"], optional=False, parallel_with=["supplier_analysis"]),
                WorkflowStep(step_id="supplier_analysis", agent_name="supplier", description="Supplier analysis", dependencies=["mysql_query"], optional=False, parallel_with=["shortage_analysis"]),
                WorkflowStep(step_id="alert_generation", agent_name="alert", description="Alert generation", dependencies=["shortage_analysis", "supplier_analysis"], optional=False)
            ],
            execution_mode=ExecutionMode.HYBRID,
            estimated_duration=180
        )
        
        with patch.object(workflow_executor, 'execute_agent_step') as mock_execute:
            mock_execute.return_value = {'status': 'success', 'results': {}}
            
            result = await workflow_executor.execute_workflow(
                pattern=hybrid_pattern,
                context=WorkflowContext(
                    workflow_id='test-hyb-001',
                    workflow_type='comprehensive',
                    user_query='Test hybrid execution'
                )
            )
            
            # Validate hybrid execution
            assert result is not None, "Hybrid execution should return results"
            assert mock_execute.call_count == 4, "Should execute all 4 steps"

    @pytest.mark.asyncio
    async def test_optional_step_handling(
        self,
        workflow_executor: WorkflowExecutor,
        sample_workflow_pattern: WorkflowPattern
    ):
        """Test handling of optional workflow steps."""
        # Mock scenario where alert step fails but it's optional
        def mock_execute_with_failure(step):
            if step.step_id == "alert_generation":
                raise Exception("Alert service unavailable")
            return {'status': 'success', 'results': {}}
        
        with patch.object(workflow_executor, 'execute_agent_step', side_effect=mock_execute_with_failure):
            result = await workflow_executor.execute_workflow(
                pattern=sample_workflow_pattern,
                context=WorkflowContext(
                    workflow_id='test-opt-001',
                    user_query='Test optional step handling'
                )
            )
            
            # Validate graceful handling of optional step failure
            assert result is not None, "Should complete workflow despite optional step failure"
            assert result.get('status') in ['completed', 'partial_success'], "Should report successful completion"
            
            # Should have warnings about optional step failure
            if 'warnings' in result:
                warnings = result['warnings']
                assert any('alert_generation' in str(warning) for warning in warnings), "Should warn about failed optional step"


class TestContextSharingMechanisms:
    """Validate context data sharing and persistence across agents."""

    @pytest_asyncio.fixture
    async def context_manager(self):
        """Create ContextManager instance for testing."""
        manager = ContextManager()
        yield manager
        # Cleanup
        await manager.cleanup_all()

    @pytest.mark.asyncio
    async def test_context_persistence_and_recovery(
        self,
        context_manager: ContextManager,
        realistic_workflow_contexts: Dict[str, WorkflowContext]
    ):
        """Validate ContextManager persistence mechanisms and context recovery."""
        workflow_context = realistic_workflow_contexts['shortage_analysis']
        
        # Store context
        await context_manager.store_context(workflow_context)
        
        # Retrieve context
        retrieved_context = await context_manager.get_context(workflow_context.workflow_id)
        
        # Validate context persistence
        assert retrieved_context is not None, "Should retrieve stored context"
        assert retrieved_context.workflow_id == workflow_context.workflow_id, "Should preserve workflow ID"
        assert retrieved_context.workflow_type == workflow_context.workflow_type, "Should preserve workflow type"
        assert retrieved_context.user_query == workflow_context.user_query, "Should preserve user query"
        
        # Test context recovery after simulated failure
        # Clear in-memory cache
        if hasattr(context_manager, '_context_cache'):
            context_manager._context_cache.clear()
        
        # Should still be able to recover from persistent storage
        recovered_context = await context_manager.get_context(workflow_context.workflow_id)
        assert recovered_context is not None, "Should recover context from persistent storage"

    @pytest.mark.asyncio
    async def test_context_data_format_validation(
        self,
        context_manager: ContextManager
    ):
        """Ensure context data remains consistent during streaming operations."""
        # Create context with MySQL data
        mysql_context = MySQLContextData(
            query_results={'orders': [{'id': 'CUSTORD-001', 'status': 'pending'}]},
            extracted_entities={'orders': ['CUSTORD-001']},
            execution_metadata={'query_time': '2024-11-01T10:00:00Z'}
        )
        
        # Create workflow context using the context manager
        workflow_context = context_manager.create_context(
            workflow_id='test-ctx-001',
            query='Test context sharing',
            query_type='shortage_analysis',
            workflow_pattern='shortage_analysis'
        )
        
        # Update MySQL context with test data
        mysql_test_data = {
            "query": "Test MySQL query",
            "response": "Test MySQL response with order data",
            "success": True,
            "entities": {"orders": ["CUSTORD-20241101001"], "materials": ["MM2004"]}
        }
        context_manager.update_mysql_context('test-ctx-001', mysql_test_data, 1.5)
        
        # Get context for shortage agent (should include MySQL results)
        shortage_context = context_manager.get_context_for_agent(
            workflow_id='test-ctx-001',
            agent_type='shortage_analyzer',
            include_full_history=True
        )
        
        # Validate context format for shortage agent (returns string summary)
        assert shortage_context is not None, "Should provide context for shortage agent"
        assert isinstance(shortage_context, str), "Context should be a string summary"
        assert 'MySQL Analysis Results:' in shortage_context, "Should include MySQL results in context summary"

    @pytest.mark.asyncio
    async def test_concurrent_context_access(
        self,
        context_manager: ContextManager
    ):
        """Test concurrent access to context data."""
        # Create multiple workflow contexts
        contexts = []
        for i in range(5):
            context = WorkflowContext(
                workflow_id=f'test-concurrent-{i:03d}',
                user_query=f'Concurrent test query {i}',
                entities={'materials': [f'MM200{i}']}
            )
            contexts.append(context)
        
        # Store contexts concurrently
        store_tasks = [context_manager.store_context(ctx) for ctx in contexts]
        await asyncio.gather(*store_tasks)
        
        # Retrieve contexts concurrently
        retrieve_tasks = [context_manager.get_context(ctx.workflow_id) for ctx in contexts]
        retrieved_contexts = await asyncio.gather(*retrieve_tasks)
        
        # Validate all contexts were stored and retrieved correctly
        assert len(retrieved_contexts) == 5, "Should retrieve all 5 contexts"
        
        for i, retrieved in enumerate(retrieved_contexts):
            original = contexts[i]
            assert retrieved is not None, f"Should retrieve context {i}"
            assert retrieved.workflow_id == original.workflow_id, f"Should preserve workflow ID for context {i}"
            assert retrieved.user_query == original.user_query, f"Should preserve query for context {i}"

    @pytest.mark.asyncio
    async def test_context_cleanup_mechanisms(
        self,
        context_manager: ContextManager
    ):
        """Test context cleanup and garbage collection."""
        # Create temporary contexts
        temp_contexts = []
        for i in range(3):
            context = WorkflowContext(
                workflow_id=f'temp-{i}',
                workflow_type='test',
                user_query=f'Temporary context {i}'
            )
            temp_contexts.append(context)
            await context_manager.store_context(context)
        
        # Verify contexts exist
        for context in temp_contexts:
            retrieved = await context_manager.get_context(context.workflow_id)
            assert retrieved is not None, f"Context {context.workflow_id} should exist before cleanup"
        
        # Cleanup specific contexts
        for context in temp_contexts:
            await context_manager.cleanup_context(context.workflow_id)
        
        # Verify contexts are cleaned up
        for context in temp_contexts:
            retrieved = await context_manager.get_context(context.workflow_id)
            assert retrieved is None, f"Context {context.workflow_id} should be cleaned up"


class TestConcurrentExecutionHandling:
    """Test parallel workflow execution capabilities."""

    @pytest.fixture
    def concurrent_queries(self):
        """Provide concurrent query test data."""
        return [
            {'query': 'Check shortage for MM2004', 'priority': 'HIGH'},
            {'query': 'Analyze supplier SteelWorks Ltd', 'workflow_type': 'supplier_risk', 'priority': 'MEDIUM'},
            {'query': 'Prioritize customer orders', 'workflow_type': 'customer_priority', 'priority': 'LOW'},
            {'query': 'Generate comprehensive report', 'workflow_type': 'comprehensive', 'priority': 'MEDIUM'},
            {'query': 'Check order CUSTORD-20241101001', 'priority': 'HIGH'}
        ]

    @pytest.mark.asyncio
    async def test_parallel_workflow_execution_coordination(
        self,
        mock_orchestration_runner,
        concurrent_queries: List[Dict[str, Any]],
        performance_monitor
    ):
        """Test ExecutionMode.PARALLEL and ExecutionMode.HYBRID workflow execution."""
        # Mock parallel execution with different completion times
        async def mock_execute_with_delay(query, **kwargs):
            import asyncio
            # Determine delay based on query content
            delays = {'shortage': 1.0, 'supplier': 2.0, 'customer': 1.5, 'comprehensive': 3.0}
            delay = 1.0
            for keyword, delay_time in delays.items():
                if keyword in query.lower():
                    delay = delay_time
                    break
            
            await asyncio.sleep(delay / 10)  # Scale down for testing
            return {
                'workflow_id': f'wf-{hash(query)}-001',
                'status': 'completed',
                'results': {'query': query},
                'execution_time': delay
            }
        
        mock_orchestration_runner.execute_financial_query.side_effect = mock_execute_with_delay
        
        performance_monitor.start_timing('parallel_execution')
        
        # Execute workflows concurrently
        tasks = []
        for query_data in concurrent_queries:
            task = mock_orchestration_runner.execute_financial_query(
                query=query_data['query']
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        
        parallel_time = performance_monitor.end_timing('parallel_execution')
        
        # Validate parallel execution
        assert len(results) == len(concurrent_queries), "Should complete all concurrent workflows"
        
        for i, result in enumerate(results):
            assert result is not None, f"Workflow {i} should return results"
            assert result.get('status') == 'completed', f"Workflow {i} should complete successfully"
        
        # Parallel execution should be faster than sequential
        # (Total sequential time would be sum of all delays, parallel should be max delay)
        max_individual_time = max(result['execution_time'] for result in results)
        assert parallel_time < max_individual_time * 1.5, "Parallel execution should be more efficient than sequential"

    @pytest.mark.asyncio
    async def test_max_concurrent_workflows_limit(
        self,
        mock_orchestration_runner
    ):
        """Test max_concurrent_workflows configuration limits."""
        max_concurrent = 3  # Set limit
        
        # Create more workflows than the limit
        workflow_count = 5
        workflows = []
        
        for i in range(workflow_count):
            workflow = {
                'query': f'Test workflow {i}',
                'workflow_id': f'wf-limit-{i}'
            }
            workflows.append(workflow)
        
        # Mock execution with tracking
        executing_count = 0
        max_executing = 0
        
        async def mock_execute_with_tracking(query, workflow_type):
            nonlocal executing_count, max_executing
            executing_count += 1
            max_executing = max(max_executing, executing_count)
            
            # Simulate work
            await asyncio.sleep(0.1)
            
            executing_count -= 1
            return {'status': 'completed', 'workflow_id': f'test-{executing_count}'}
        
        mock_orchestration_runner.execute_financial_query.side_effect = mock_execute_with_tracking
        
        # Execute with concurrency control
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def execute_with_limit(workflow):
            async with semaphore:
                return await mock_orchestration_runner.execute_financial_query(
                    query=workflow['query'],
                    workflow_type=workflow['workflow_type']
                )
        
        tasks = [execute_with_limit(wf) for wf in workflows]
        results = await asyncio.gather(*tasks)
        
        # Validate concurrency limit was respected
        assert max_executing <= max_concurrent, f"Should not exceed {max_concurrent} concurrent workflows, got {max_executing}"
        assert len(results) == workflow_count, "Should complete all workflows despite limit"

    @pytest.mark.asyncio
    async def test_workflow_priority_handling(
        self,
        concurrent_queries: List[Dict[str, Any]]
    ):
        """Test workflow prioritization in concurrent execution."""
        # Sort queries by priority (HIGH, MEDIUM, LOW)
        priority_order = {'HIGH': 0, 'MEDIUM': 1, 'LOW': 2}
        sorted_queries = sorted(concurrent_queries, key=lambda q: priority_order[q['priority']])
        
        execution_order = []
        
        async def mock_execute_with_order_tracking(query, workflow_type):
            execution_order.append((query, workflow_type))
            await asyncio.sleep(0.05)  # Small delay
            return {'status': 'completed', 'query': query}
        
        # Execute with priority-based scheduling
        high_priority_tasks = []
        medium_priority_tasks = []
        low_priority_tasks = []
        
        for query_data in concurrent_queries:
            task = mock_execute_with_order_tracking(query_data['query'], query_data['workflow_type'])
            
            if query_data['priority'] == 'HIGH':
                high_priority_tasks.append(task)
            elif query_data['priority'] == 'MEDIUM':
                medium_priority_tasks.append(task)
            else:
                low_priority_tasks.append(task)
        
        # Execute in priority order
        high_results = await asyncio.gather(*high_priority_tasks) if high_priority_tasks else []
        medium_results = await asyncio.gather(*medium_priority_tasks) if medium_priority_tasks else []
        low_results = await asyncio.gather(*low_priority_tasks) if low_priority_tasks else []
        
        all_results = high_results + medium_results + low_results
        
        # Validate priority execution
        assert len(all_results) == len(concurrent_queries), "Should execute all workflows"
        
        # Validate that high-priority workflows executed first
        high_priority_queries = [q['query'] for q in concurrent_queries if q['priority'] == 'HIGH']
        if high_priority_queries:
            first_executed_queries = [query for query, _ in execution_order[:len(high_priority_queries)]]
            for hpq in high_priority_queries:
                assert hpq in first_executed_queries, f"High priority query '{hpq}' should execute first"


class TestWorkflowPatternExecution:
    """Validate different workflow patterns (shortage_analysis, supplier_risk, customer_priority, comprehensive)."""

    @pytest.fixture
    def workflow_patterns(self):
        """Provide workflow pattern test data."""
        return {
            'shortage_analysis': {
                'expected_agents': ['mysql', 'shortage', 'alert'],
                'expected_steps': ['mysql_query', 'shortage_analysis', 'alert_generation'],
                'min_duration': 2.0,
                'max_duration': 10.0
            },
            'supplier_risk': {
                'expected_agents': ['mysql', 'supplier', 'alert'],
                'expected_steps': ['mysql_query', 'supplier_analysis', 'alert_generation'],
                'min_duration': 2.5,
                'max_duration': 12.0
            },
            'customer_priority': {
                'expected_agents': ['mysql', 'priority', 'alert'],
                'expected_steps': ['mysql_query', 'priority_analysis', 'alert_generation'],
                'min_duration': 1.5,
                'max_duration': 8.0
            },
            'comprehensive': {
                'expected_agents': ['mysql', 'shortage', 'supplier', 'priority', 'alert'],
                'expected_steps': ['mysql_query', 'shortage_analysis', 'supplier_analysis', 'priority_analysis', 'alert_generation'],
                'min_duration': 5.0,
                'max_duration': 20.0
            }
        }

    @pytest.mark.parametrize("pattern_name", ['shortage_analysis', 'supplier_risk', 'customer_priority', 'comprehensive'])
    @pytest.mark.asyncio
    async def test_workflow_pattern_structure_validation(
        self,
        pattern_name: str,
        workflow_patterns: Dict[str, Dict[str, Any]]
    ):
        """Validate workflow pattern structure and configuration."""
        registry = WorkflowPatternRegistry()
        pattern = registry.get_pattern_by_name(pattern_name)
        
        assert pattern is not None, f"Pattern {pattern_name} should exist"
        assert pattern.name == pattern_name, f"Pattern name should match {pattern_name}"
        
        expected = workflow_patterns[pattern_name]
        
        # Validate workflow steps
        assert len(pattern.steps) > 0, f"Pattern {pattern_name} should have steps"
        
        step_names = [step.name for step in pattern.steps]
        for expected_step in expected['expected_steps']:
            if expected_step in step_names:  # Some steps might be renamed
                assert expected_step in step_names or any(expected_step.replace('_', '') in step.replace('_', '') for step in step_names), f"Pattern should include step like {expected_step}"
        
        # Validate estimated duration
        if pattern.estimated_duration:
            assert expected['min_duration'] <= pattern.estimated_duration <= expected['max_duration'], f"Duration should be reasonable for {pattern_name}"

    @pytest.mark.asyncio
    async def test_workflow_pattern_dependency_chains(self):
        """Test workflow step dependencies form valid chains."""
        registry = WorkflowPatternRegistry()
        
        pattern_names = ['shortage_analysis', 'supplier_risk', 'customer_priority', 'comprehensive']
        
        for pattern_name in pattern_names:
            pattern = registry.get_pattern_by_name(pattern_name)
            if pattern is None:
                continue
                
            # Validate dependency chains
            step_names = {step.name for step in pattern.steps}
            
            for step in pattern.steps:
                # All dependencies should reference valid steps
                for dep in step.dependencies:
                    assert dep in step_names, f"Dependency {dep} should reference valid step in pattern {pattern_name}"
                
                # No circular dependencies (basic check)
                assert step.name not in step.dependencies, f"Step {step.name} should not depend on itself"

    @pytest.mark.asyncio
    async def test_workflow_pattern_execution_modes(self):
        """Test different execution modes in workflow patterns."""
        registry = WorkflowPatternRegistry()
        
        # Test each pattern has a valid execution mode
        test_patterns = ['shortage_analysis', 'supplier_risk', 'customer_priority', 'comprehensive']
        
        for pattern_name in test_patterns:
            pattern = registry.get_pattern_by_name(pattern_name)
            if pattern is None:
                continue
            
            assert hasattr(pattern, 'execution_mode'), f"Pattern {pattern_name} should have execution mode"
            
            if pattern.execution_mode:
                valid_modes = [ExecutionMode.SEQUENTIAL, ExecutionMode.PARALLEL, ExecutionMode.HYBRID]
                assert pattern.execution_mode in valid_modes, f"Pattern {pattern_name} should have valid execution mode"

    @pytest.mark.asyncio
    async def test_required_vs_optional_steps(self):
        """Test required vs optional step handling in patterns."""
        registry = WorkflowPatternRegistry()
        
        # Test shortage analysis pattern
        pattern = registry.get_pattern_by_name('shortage_analysis')
        if pattern:
            # Core steps should be required
            required_steps = [step for step in pattern.steps if step.required]
            optional_steps = [step for step in pattern.steps if not step.required]
            
            assert len(required_steps) > 0, "Should have at least one required step"
            
            # MySQL and shortage analysis should typically be required
            required_names = {step.name for step in required_steps}
            expected_required = ['mysql_query', 'shortage_analysis']
            
            for expected in expected_required:
                matching_steps = [name for name in required_names if expected.replace('_', '') in name.replace('_', '')]
                assert len(matching_steps) > 0, f"Should have required step matching {expected}"
            
            # Alert generation might be optional
            optional_names = {step.name for step in optional_steps}
            if optional_names:
                # At least one optional step should relate to alerts/notifications
                has_alert_optional = any('alert' in name.lower() or 'notification' in name.lower() for name in optional_names)
                # This is acceptable but not required