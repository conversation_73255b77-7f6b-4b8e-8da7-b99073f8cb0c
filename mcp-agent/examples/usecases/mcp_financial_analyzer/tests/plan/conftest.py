"""Plan-specific pytest configuration and fixtures for TDD test suite.

Extends the main conftest.py without breaking global test infrastructure.
Provides comprehensive fixtures for real agent factories, mock agents,
orchestration runners, test data, service health, and performance monitoring.
"""

import asyncio
import json
import logging
import os
import sys
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Generator
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
import pytest_asyncio
from httpx import AsyncClient
from pydantic import BaseModel

# Add the parent directory to sys.path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import from main test infrastructure
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
try:
    from conftest import *
except ImportError:
    # If main conftest is not available, we'll define our own fixtures
    pass

# Import orchestration components
from orchestrator.orchestration_runner import OrchestrationRunner, create_orchestration_runner
from orchestrator.financial_orchestrator import FinancialOrchestrator
from orchestrator.context_manager import ContextManager, FinancialWorkflowContext as WorkflowContext
from orchestrator.workflow_patterns import WorkflowPatternRegistry, WorkflowPattern
from agents.mysql_agent import create_mysql_orchestrator_agent as create_mysql_agent
from agents.shortage_analyzer_agent import create_shortage_analyzer_agent
from agents.base_agent_wrapper import BaseAgentWrapper
from agents.alert_manager_agent import AlertManagerAgent, create_alert_manager_agent
from schemas.agent_schemas import ShortageAnalysisInputSchema, AlertManagementInputSchema, InstructionInputSchema, InstructionOutputSchema

# Import context data classes - fail fast if not available
from orchestrator.context_manager import MySQLContextData, ShortageContextData, AlertContextData

# Define MCPOrchestratorInputSchema if not available
try:
    from agents.mysql_agent import MCPOrchestratorInputSchema
except ImportError:
    from pydantic import BaseModel

    class MCPOrchestratorInputSchema(BaseModel):
        query: str
        entities: Dict[str, Any] = {}
        parameters: Dict[str, Any] = {}


# Service configuration
SERVICE_PORTS = {
    'mysql': 8702,
    'shortage': 6970,
    'alert': 6971
}

SERVICE_HOSTS = {
    'mysql': 'localhost',
    'shortage': 'localhost', 
    'alert': 'localhost'
}


@pytest_asyncio.fixture
async def real_mysql_agent():
    """Create a real MySQL agent instance using factory function."""
    agent = create_mysql_agent()  # This is sync, not async
    yield agent
    # Cleanup
    if hasattr(agent, 'cleanup'):
        await agent.cleanup()


@pytest_asyncio.fixture
async def real_shortage_agent():
    """Create a real Shortage Analyzer agent instance using factory function."""
    agent = create_shortage_analyzer_agent()  # This is sync, not async
    yield agent
    # Cleanup
    if hasattr(agent, 'cleanup'):
        await agent.cleanup()


@pytest_asyncio.fixture
async def real_alert_agent():
    """Create a real Alert Manager agent instance using factory function."""
    agent = create_alert_manager_agent()  # This is sync, not async
    yield agent
    # Cleanup
    if hasattr(agent, 'cleanup'):
        await agent.cleanup()


@pytest_asyncio.fixture
async def real_agent_factory():
    """Factory for creating real agent instances."""
    agents = []
    
    async def _create_agent(agent_type: str):
        if agent_type == 'mysql':
            agent = create_mysql_agent()  # This is sync, not async
        elif agent_type == 'shortage':
            agent = create_shortage_analyzer_agent()  # This is sync, not async
        elif agent_type == 'alert':
            agent = create_alert_manager_agent()  # This is sync, not async
        else:
            raise ValueError(f"Unknown agent type: {agent_type}")
        
        agents.append(agent)
        return agent
    
    yield _create_agent
    
    # Cleanup all created agents
    for agent in agents:
        if hasattr(agent, 'cleanup'):
            await agent.cleanup()


@pytest.fixture
def mock_mysql_agent():
    """Create sophisticated mock MySQL agent."""
    mock_agent = AsyncMock(spec=BaseAgentWrapper)
    
    # Mock typical MySQL responses
    mock_agent.process_query.return_value = {
        'status': 'success',
        'data': {
            'orders': [
                {'order_id': 'CUSTORD-20241101001', 'customer': 'ACME Corp', 'status': 'pending'},
                {'order_id': 'CUSTORD-20241101002', 'customer': 'TechFlow Inc', 'status': 'processing'}
            ],
            'materials': [
                {'material_code': 'MM2004', 'description': 'Steel Component', 'current_stock': 150},
                {'material_code': 'HCS500', 'description': 'High Carbon Steel', 'current_stock': 75}
            ],
            'suppliers': [
                {'supplier_name': 'SteelWorks Ltd', 'reliability_score': 0.95, 'lead_time_days': 7},
                {'supplier_name': 'MetalCorp', 'reliability_score': 0.87, 'lead_time_days': 10}
            ]
        }
    }
    
    mock_agent.get_context_data.return_value = MySQLContextData(
        query="SELECT * FROM orders",
        response="MySQL analysis completed",
        table_data={'orders': [], 'materials': [], 'suppliers': []},
        entities_found={'orders': ['CUSTORD-20241101001'], 'materials': ['MM2004']},
        success=True,
        execution_time=1.5
    )
    
    return mock_agent


@pytest.fixture
def mock_shortage_agent():
    """Create sophisticated mock Shortage Analyzer agent."""
    mock_agent = AsyncMock(spec=BaseAgentWrapper)
    
    # Mock typical shortage analysis responses
    mock_agent.enhanced_shortage_analysis.return_value = {
        'status': 'success',
        'shortage_analysis': {
            'materials': [
                {
                    'material_code': 'MM2004',
                    'shortage_index': 0.75,
                    'risk_level': 'HIGH',
                    'recommended_action': 'IMMEDIATE_ORDER'
                },
                {
                    'material_code': 'HCS500', 
                    'shortage_index': 0.35,
                    'risk_level': 'MEDIUM',
                    'recommended_action': 'MONITOR'
                }
            ],
            'overall_risk_score': 0.65,
            'priority_orders': ['CUSTORD-20241101001']
        }
    }
    
    mock_agent.get_context_data.return_value = ShortageContextData(
        company_name="Test Company",
        shortage_index=0.65,
        risk_level="MEDIUM",
        recommendations=["Monitor closely", "Consider backup suppliers"],
        response="Shortage analysis completed with medium risk",
        success=True,
        execution_time=2.1
    )
    
    return mock_agent


@pytest.fixture
def mock_alert_agent():
    """Create sophisticated mock Alert Manager agent."""
    mock_agent = AsyncMock(spec=AlertManagerAgent)
    
    # Mock typical alert responses
    mock_agent.process_financial_analysis.return_value = {
        'status': 'success',
        'alerts_sent': [
            'ALT-20241101-001'
        ],
        'notifications_delivered': 3,
        'failed_deliveries': 0
    }
    
    mock_agent.get_context_data.return_value = AlertContextData(
        company_name="Test Company",
        alerts_sent=["ALT-20241101-001"],
        notification_results=["Email sent successfully"],
        alert_summary="Processed 1 high-severity alert",
        channels_used=["email"],
        severity_level="high",
        success=True,
        execution_time=0.8
    )
    
    return mock_agent


@pytest_asyncio.fixture
async def real_orchestration_runner(
    real_mysql_agent, 
    real_shortage_agent, 
    real_alert_agent
):
    """Create a real OrchestrationRunner instance with proper agent and LLM factory setup."""
    # Create a mock LLM factory
    def mock_llm_factory():
        from unittest.mock import MagicMock
        return MagicMock()
    
    # Use create_orchestration_runner factory function
    runner = create_orchestration_runner(
        mysql_agent=real_mysql_agent,
        shortage_agent=real_shortage_agent,
        alert_agent=real_alert_agent,
        llm_factory=mock_llm_factory
    )
    
    yield runner
    
    # No cleanup needed as OrchestrationRunner doesn't have initialize/cleanup methods


@pytest.fixture
def mock_orchestration_runner():
    """Create mock OrchestrationRunner for isolated testing."""
    mock_runner = AsyncMock(spec=OrchestrationRunner)
    
    # Mock typical orchestration responses matching API reference
    mock_runner.execute_financial_query.return_value = {
        'success': True,
        'workflow_id': 'wf-test-001',
        'execution_time': 2.5,
        'execution_mode': 'pattern_based',
        'mysql_analysis': {
            'success': True,
            'response': 'MySQL analysis completed',
            'execution_time': 1.0
        },
        'shortage_analysis': {
            'success': True,
            'shortage_index': 0.5,
            'risk_level': 'MEDIUM',
            'execution_time': 1.2
        },
        'alert_management': {
            'success': True,
            'alerts_sent': [],
            'execution_time': 0.3
        }
    }
    
    mock_runner.get_execution_statistics.return_value = {
        'total_workflows': 1,
        'successful_workflows': 1,
        'failed_workflows': 0,
        'average_execution_time': 2.5,
        'agent_performance': {
            'mysql_analyzer': {'avg_time': 0.8, 'success_rate': 1.0},
            'shortage_analyzer': {'avg_time': 1.2, 'success_rate': 1.0},
            'alert_manager': {'avg_time': 0.5, 'success_rate': 1.0}
        }
    }
    
    return mock_runner


@pytest.fixture
def realistic_financial_queries():
    """Provide realistic financial query test data."""
    return [
        {
            'query': 'What is the shortage status for order CUSTORD-20241101001?',
            'expected_entities': {'orders': ['CUSTORD-20241101001']},
            'expected_workflow': 'shortage_analysis',
            'query_type': 'shortage_inquiry'
        },
        {
            'query': 'Check supplier reliability for materials MM2004 and HCS500',
            'expected_entities': {'materials': ['MM2004', 'HCS500']},
            'expected_workflow': 'supplier_risk',
            'query_type': 'supplier_analysis'
        },
        {
            'query': 'Prioritize customer orders based on shortage risk',
            'expected_entities': {},
            'expected_workflow': 'customer_priority',
            'query_type': 'priority_management'
        },
        {
            'query': 'Generate comprehensive financial analysis for Q4 operations',
            'expected_entities': {},
            'expected_workflow': 'comprehensive',
            'query_type': 'comprehensive_analysis'
        }
    ]


@pytest.fixture
def realistic_workflow_contexts():
    """Provide realistic workflow context test data."""
    return {
        'shortage_analysis': WorkflowContext(
            workflow_id='wf-shortage-001',
            workflow_type='shortage_analysis',
            user_query='Check shortage for MM2004',
            entities={'materials': ['MM2004']},
            current_step='mysql_query',
            agent_contexts={},
            metadata={'created_at': '2024-11-01T10:00:00Z'}
        ),
        'supplier_risk': WorkflowContext(
            workflow_id='wf-supplier-001', 
            workflow_type='supplier_risk',
            user_query='Analyze supplier reliability',
            entities={'suppliers': ['SteelWorks Ltd']},
            current_step='mysql_query',
            agent_contexts={},
            metadata={'created_at': '2024-11-01T10:00:00Z'}
        )
    }


@pytest.fixture
def realistic_financial_scenarios():
    """Provide realistic financial scenario data for testing."""
    return {
        'critical_shortage': {
            'materials': [
                {'code': 'MM2004', 'current_stock': 5, 'required': 150, 'shortage_index': 0.95},
                {'code': 'HCS500', 'current_stock': 20, 'required': 100, 'shortage_index': 0.80}
            ],
            'orders': [
                {'id': 'CUSTORD-20241101001', 'priority': 'HIGH', 'due_date': '2024-11-05'},
                {'id': 'CUSTORD-20241101002', 'priority': 'MEDIUM', 'due_date': '2024-11-08'}
            ],
            'suppliers': [
                {'name': 'SteelWorks Ltd', 'reliability': 0.95, 'lead_time': 7},
                {'name': 'MetalCorp', 'reliability': 0.80, 'lead_time': 14}
            ]
        },
        'normal_operations': {
            'materials': [
                {'code': 'MM2004', 'current_stock': 200, 'required': 150, 'shortage_index': 0.15},
                {'code': 'HCS500', 'current_stock': 120, 'required': 100, 'shortage_index': 0.20}
            ],
            'orders': [
                {'id': 'CUSTORD-20241101003', 'priority': 'MEDIUM', 'due_date': '2024-11-10'}
            ],
            'suppliers': [
                {'name': 'SteelWorks Ltd', 'reliability': 0.95, 'lead_time': 7}
            ]
        }
    }


@pytest_asyncio.fixture
async def service_health_checker(real_orchestration_runner):
    """Extended service health checking using async connectivity validation."""
    async def _check_all_services() -> Dict[str, bool]:
        try:
            # Use OrchestrationRunner's validate_agent_connectivity method
            connectivity = await real_orchestration_runner.validate_agent_connectivity()
            return connectivity
        except Exception as e:
            # Fallback to False for all services if validation fails
            logger.warning(f"Service health check failed: {e}")
            return {
                'mysql_analyzer': False,
                'shortage_analyzer': False,
                'alert_manager': False
            }
    
    return _check_all_services


@pytest.fixture
def jsonl_log_parser():
    """Setup for parsing JSONL logs to validate TOOL_CALL events."""
    def _parse_jsonl_file(log_path: str) -> List[Dict[str, Any]]:
        """Parse JSONL log file and return list of log entries."""
        entries = []
        if os.path.exists(log_path):
            with open(log_path, 'r') as f:
                for line in f:
                    try:
                        entry = json.loads(line.strip())
                        entries.append(entry)
                    except json.JSONDecodeError:
                        continue
        return entries
    
    def _extract_tool_calls(entries: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """Extract TOOL_CALL_START/ARGS/END events from log entries."""
        tool_events = {
            'start': [],
            'args': [],
            'end': []
        }
        
        for entry in entries:
            event_type = entry.get('event_type', '')
            if event_type == 'TOOL_CALL_START':
                tool_events['start'].append(entry)
            elif event_type == 'TOOL_CALL_ARGS':
                tool_events['args'].append(entry)
            elif event_type == 'TOOL_CALL_END':
                tool_events['end'].append(entry)
        
        return tool_events
    
    def _validate_tool_execution_sequence(tool_events: Dict[str, List[Dict[str, Any]]]) -> bool:
        """Validate that tool calls have proper START -> ARGS -> END sequence."""
        start_events = {event['tool_call_id']: event for event in tool_events['start']}
        args_events = {event['tool_call_id']: event for event in tool_events['args']}
        end_events = {event['tool_call_id']: event for event in tool_events['end']}
        
        # Check that every START has corresponding ARGS and END
        for tool_call_id in start_events:
            if tool_call_id not in args_events or tool_call_id not in end_events:
                return False
        
        return True
    
    return {
        'parse_file': _parse_jsonl_file,
        'extract_tool_calls': _extract_tool_calls,
        'validate_sequence': _validate_tool_execution_sequence
    }


@pytest.fixture
def performance_monitor():
    """Fixtures for measuring execution times and streaming latency."""
    class PerformanceMonitor:
        def __init__(self):
            self.metrics = {}
            self.start_times = {}
        
        def start_timing(self, operation: str):
            """Start timing an operation."""
            self.start_times[operation] = time.time()
        
        def end_timing(self, operation: str) -> float:
            """End timing and return duration in seconds."""
            if operation not in self.start_times:
                raise ValueError(f"No start time recorded for operation: {operation}")
            
            duration = time.time() - self.start_times[operation]
            self.metrics[operation] = duration
            del self.start_times[operation]
            return duration
        
        def get_metric(self, operation: str) -> Optional[float]:
            """Get recorded metric for operation."""
            return self.metrics.get(operation)
        
        def get_all_metrics(self) -> Dict[str, float]:
            """Get all recorded metrics."""
            return self.metrics.copy()
        
        def assert_latency_under(self, operation: str, max_latency: float):
            """Assert operation latency is under threshold."""
            actual_latency = self.metrics.get(operation)
            if actual_latency is None:
                raise ValueError(f"No metric recorded for operation: {operation}")
            
            assert actual_latency < max_latency, f"Operation {operation} took {actual_latency}s, expected < {max_latency}s"
    
    return PerformanceMonitor()


@pytest.fixture
def streaming_latency_validator():
    """Validator for streaming response latency requirements."""
    async def _measure_streaming_latency(async_generator, max_latency_ms: int = 100):
        """Measure latency between streaming chunks."""
        latencies = []
        last_time = time.time()
        
        async for chunk in async_generator:
            current_time = time.time()
            latency_ms = (current_time - last_time) * 1000
            latencies.append(latency_ms)
            last_time = current_time
            
            # Validate each chunk latency
            if latency_ms > max_latency_ms:
                raise AssertionError(f"Streaming latency {latency_ms}ms exceeds limit {max_latency_ms}ms")
        
        return {
            'latencies': latencies,
            'avg_latency': sum(latencies) / len(latencies) if latencies else 0,
            'max_latency': max(latencies) if latencies else 0,
            'min_latency': min(latencies) if latencies else 0
        }
    
    return _measure_streaming_latency


@pytest.fixture
def memory_usage_monitor():
    """Monitor memory usage during test execution."""
    import psutil
    import os
    
    class MemoryMonitor:
        def __init__(self):
            self.process = psutil.Process(os.getpid())
            self.initial_memory = None
            self.peak_memory = 0
        
        def start_monitoring(self):
            """Start memory monitoring."""
            self.initial_memory = self.process.memory_info().rss / 1024 / 1024  # MB
            self.peak_memory = self.initial_memory
        
        def update_peak(self):
            """Update peak memory usage."""
            current_memory = self.process.memory_info().rss / 1024 / 1024  # MB
            if current_memory > self.peak_memory:
                self.peak_memory = current_memory
        
        def get_memory_usage(self) -> Dict[str, float]:
            """Get current memory usage statistics."""
            current_memory = self.process.memory_info().rss / 1024 / 1024  # MB
            return {
                'initial_mb': self.initial_memory or 0,
                'current_mb': current_memory,
                'peak_mb': self.peak_memory,
                'increase_mb': current_memory - (self.initial_memory or 0)
            }
        
        def assert_memory_increase_under(self, max_increase_mb: float):
            """Assert memory increase is under threshold."""
            usage = self.get_memory_usage()
            assert usage['increase_mb'] < max_increase_mb, f"Memory increased by {usage['increase_mb']}MB, expected < {max_increase_mb}MB"
    
    return MemoryMonitor()


@pytest.fixture
def large_dataset_generator():
    """Generate large financial datasets for performance testing."""
    def _generate_orders(count: int) -> List[Dict[str, Any]]:
        """Generate realistic order data."""
        orders = []
        for i in range(count):
            order_id = f"CUSTORD-2024110{i:04d}"
            orders.append({
                'order_id': order_id,
                'customer': f'Customer_{i % 50}',  # 50 different customers
                'status': ['pending', 'processing', 'completed'][i % 3],
                'priority': ['HIGH', 'MEDIUM', 'LOW'][i % 3],
                'due_date': f'2024-11-{(i % 30) + 1:02d}',
                'total_value': 1000 + (i * 100) % 50000
            })
        return orders
    
    def _generate_materials(count: int) -> List[Dict[str, Any]]:
        """Generate realistic material data."""
        materials = []
        material_prefixes = ['MM', 'HCS', 'DDR', 'SSD', 'CPU']
        
        for i in range(count):
            prefix = material_prefixes[i % len(material_prefixes)]
            material_code = f"{prefix}{2000 + i:04d}"
            materials.append({
                'material_code': material_code,
                'description': f'Material {material_code} Description',
                'current_stock': 50 + (i * 25) % 1000,
                'required_stock': 100 + (i * 30) % 800,
                'unit_cost': 10 + (i * 5) % 500,
                'supplier_count': 1 + (i % 5)
            })
        return materials
    
    def _generate_suppliers(count: int) -> List[Dict[str, Any]]:
        """Generate realistic supplier data."""
        suppliers = []
        for i in range(count):
            suppliers.append({
                'supplier_name': f'Supplier_{i:03d}_Corp',
                'reliability_score': 0.7 + (i * 0.003) % 0.3,  # 0.7 to 1.0
                'lead_time_days': 5 + (i % 20),  # 5 to 24 days
                'cost_rating': ['LOW', 'MEDIUM', 'HIGH'][i % 3],
                'quality_rating': 3 + (i % 3),  # 3 to 5 stars
                'active': i % 10 != 0  # 90% active
            })
        return suppliers
    
    return {
        'orders': _generate_orders,
        'materials': _generate_materials,
        'suppliers': _generate_suppliers
    }


@pytest.fixture
def sample_financial_data():
    """Provide sample financial data for testing."""
    return {
        'orders': [
            {
                'order_id': 'CUSTORD-20241101001',
                'customer_name': 'TechCorp Industries',
                'order_date': '2024-11-01',
                'priority': 'HIGH',
                'total_value': 125000.00,
                'status': 'PENDING'
            },
            {
                'order_id': 'WO-20241102001',
                'customer_name': 'Manufacturing Solutions',
                'order_date': '2024-11-02',
                'priority': 'MEDIUM',
                'total_value': 75000.00,
                'status': 'IN_PROGRESS'
            }
        ],
        'materials': [
            {
                'material_code': 'MM2004',
                'description': 'Steel Rod 10mm',
                'current_stock': 150,
                'required_stock': 500,
                'unit_cost': 25.50,
                'supplier': 'SteelWorks Ltd',
                'lead_time_days': 14
            },
            {
                'material_code': 'HCS500',
                'description': 'High Carbon Steel',
                'current_stock': 0,
                'required_stock': 200,
                'unit_cost': 45.00,
                'supplier': 'MetalSupply Co',
                'lead_time_days': 21
            }
        ],
        'suppliers': [
            {
                'supplier_name': 'SteelWorks Ltd',
                'reliability_score': 0.95,
                'delivery_performance': 0.92,
                'quality_rating': 4.8,
                'active': True
            },
            {
                'supplier_name': 'MetalSupply Co',
                'reliability_score': 0.87,
                'delivery_performance': 0.89,
                'quality_rating': 4.5,
                'active': True
            }
        ]
    }


@pytest_asyncio.fixture(autouse=True)
async def cleanup_workflow_contexts():
    """Automatically cleanup workflow contexts after each test."""
    yield
    # Cleanup any persistent contexts
    try:
        context_manager = ContextManager()
        # Iterate over active contexts and clean them up
        for workflow_id in list(context_manager.active_contexts.keys()):
            context_manager.cleanup_context(workflow_id)
    except Exception as e:
        # Ignore cleanup errors during test teardown
        pass